import { ApiProperty, PickType } from '@nestjs/swagger';
import { Decimal } from '@prisma/client/runtime/library';
import { Transform, Type } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString
} from 'class-validator';

import { AuditStatus, SettlementType, SubmitStatus } from '@/prisma/generated';

export class BaseMaterialSettlementDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '单据编码' })
  @IsNotEmpty({ message: '单据编码不能为空' })
  @IsString({ message: '单据编码必须是字符串' })
  code: string;

  @ApiProperty({ description: '结算类型' })
  @IsNotEmpty({ message: '结算类型不能为空' })
  @IsIn(Object.values(SettlementType), {
    message: '结算类型必须是有效枚举值'
  })
  @IsEnum(SettlementType, { message: '结算类型必须是有效枚举值' })
  settlementType: SettlementType;

  @ApiProperty({ description: '供应商ID' })
  @IsOptional({ message: '供应商ID不能为空' })
  @IsString({ message: '供应商ID必须是字符串' })
  supplierId?: string;

  @ApiProperty({ description: '供应商名称' })
  @IsOptional({ message: '供应商名称不能为空' })
  @IsString({ message: '供应商名称必须是字符串' })
  supplierName?: string;

  @ApiProperty({ description: '合同ID' })
  @IsOptional({ message: '合同ID可以为空' })
  @IsString({ message: '合同ID必须是字符串' })
  contractId?: string | null;

  @ApiProperty({ description: '合同名称' })
  @IsOptional({ message: '合同名称可以为空' })
  @IsString({ message: '合同名称必须是字符串' })
  contractName?: string | null;

  @ApiProperty({ description: '价格类型' })
  @IsOptional({ message: '价格类型可以为空' })
  @IsString({ message: '价格类型必须是字符串' })
  priceType?: string | null;

  @ApiProperty({ description: '合同税率' })
  @IsOptional({ message: '合同税率可以为空' })
  @IsNumber({}, { message: '合同税率必须是数字' })
  taxRate?: number | null;

  @ApiProperty({ description: '合同金额' })
  @IsOptional({ message: '合同金额可以为空' })
  @IsNumber({}, { message: '合同金额必须是数字' })
  contractAmount?: number | null;

  @ApiProperty({ description: '不含税金额' })
  @IsOptional({ message: '不含税金额可以为空' })
  @IsNumber({}, { message: '不含税金额必须是数字' })
  taxExcludedAmount?: number | null;

  @ApiProperty({ description: '含税金额' })
  @IsOptional({ message: '含税金额可以为空' })
  @IsNumber({}, { message: '含税金额必须是数字' })
  taxIncludedAmount?: number | null;

  @ApiProperty({ description: '税金' })
  @IsOptional({ message: '税金可以为空' })
  @IsNumber({}, { message: '税金必须是数字' })
  taxAmount?: number | null;

  @ApiProperty({ description: '期前开累不含税金额' })
  @IsOptional({ message: '期前开累不含税金额可以为空' })
  @IsNumber({}, { message: '期前开累不含税金额必须是数字' })
  beforeCumulationTaxExcludedAmount?: Decimal | null;

  @ApiProperty({ description: '期前开累含税金额' })
  @IsOptional({ message: '期前开累含税金额可以为空' })
  @IsNumber({}, { message: '期前开累含税金额必须是数字' })
  beforeCumulationTaxIncludedAmount?: Decimal | null;

  @ApiProperty({ description: '期前开累税金' })
  @IsOptional({ message: '期前开累税金可以为空' })
  @IsNumber({}, { message: '期前开累税金必须是数字' })
  beforeCumulationTaxAmount?: Decimal | null;

  @ApiProperty({ description: '开累含税金额' })
  @IsOptional({ message: '开累含税金额可以为空' })
  @IsNumber({}, { message: '开累含税金额必须是数字' })
  cumulationTaxIncludedAmount?: number | null;

  @ApiProperty({ description: '提交状态' })
  @IsNotEmpty({ message: '提交状态不能为空' })
  @IsIn(Object.values(SubmitStatus), {
    message: '提交状态必须是有效枚举值'
  })
  @IsString({ message: '提交状态必须是字符串' })
  submitStatus: SubmitStatus;

  @ApiProperty({ description: '审批状态' })
  @IsNotEmpty({ message: '审批状态不能为空' })
  @IsIn(Object.values(AuditStatus), {
    message: '审批状态必须是有效枚举值'
  })
  @IsString({ message: '审批状态必须是字符串' })
  auditStatus: AuditStatus;

  @ApiProperty({ description: '年' })
  @IsOptional({ message: '年可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '年必须是数字' })
  year?: number;

  @ApiProperty({ description: '月' })
  @IsOptional({ message: '月可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '月必须是数字' })
  month?: number;

  @ApiProperty({ description: '创建人名称' })
  @IsNotEmpty({ message: '创建人名称不能为空' })
  @IsString({ message: '创建人名称必须是字符串' })
  creator: string;

  @ApiProperty({ description: '编制时间' })
  createAt: Date;

  @ApiProperty({ description: '结算日期' })
  @IsNotEmpty({ message: '结算日期不能为空' })
  @IsString({ message: '结算日期必须是字符串' })
  settlementDate: string;
}

export class MaterialSettlementSubmitBodyDto extends PickType(
  BaseMaterialSettlementDto,
  ['id', 'submitStatus']
) {}

export class MaterialSettlementAuditBodyDto extends PickType(
  BaseMaterialSettlementDto,
  ['id', 'auditStatus']
) {}

export class MaterialSettlementPeriodResDto extends PickType(
  BaseMaterialSettlementDto,
  ['year', 'month', 'supplierId', 'contractId']
) {
  @ApiProperty({ description: '是否获取开累' })
  @IsOptional({ message: '是否获取开累可以为空' })
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: '是否获取开累必须是布尔' })
  isCumulation?: boolean;

  @ApiProperty({ description: 'materialCategoryId' })
  @IsOptional({ message: 'materialCategoryId可以为空' })
  @IsString({ message: 'materialCategoryId必须是字符串' })
  materialCategoryId?: string;

  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id可以为空' })
  @IsString({ message: 'id必须是字符串' })
  id?: string;
}

export class MaterialSettlementAmountResDto extends PickType(
  BaseMaterialSettlementDto,
  ['taxIncludedAmount', 'cumulationTaxIncludedAmount']
) {}

export class MaterialSettlementContractResDto {
  @ApiProperty({ description: '应结算' })
  @IsOptional({ message: '应结算可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '应结算必须是数字' })
  allCount?: number;

  @ApiProperty({ description: '已结算' })
  @IsOptional({ message: '已结算可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '已结算必须是数字' })
  settledCount?: number;

  @ApiProperty({ description: '未结算' })
  @IsOptional({ message: '未结算可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '未结算必须是数字' })
  unSettledCount?: number;
}

export class MaterialSettlementMaterialResDto extends PickType(
  BaseMaterialSettlementDto,
  ['taxIncludedAmount', 'cumulationTaxIncludedAmount']
) {
  @ApiProperty({ description: 'materialCategoryId' })
  @IsNotEmpty({ message: 'materialCategoryId不能为空' })
  @IsString({ message: 'materialCategoryId必须是字符串' })
  materialCategoryId: string;

  @ApiProperty({ description: 'materialCategoryName' })
  @IsNotEmpty({ message: 'materialCategoryName不能为空' })
  @IsString({ message: 'materialCategoryName必须是字符串' })
  materialCategoryName: string;
}

export class MaterialSettlementCreateDto extends PickType(
  BaseMaterialSettlementDto,
  ['settlementType']
) {}

export class MaterialSettlementUpdateDto extends PickType(
  BaseMaterialSettlementDto,
  [
    'supplierId',
    'supplierName',
    'contractId',
    'contractName',
    'settlementDate',
    'taxRate',
    'priceType',
    'contractAmount',
    'year',
    'month',
    'beforeCumulationTaxExcludedAmount',
    'beforeCumulationTaxIncludedAmount',
    'beforeCumulationTaxAmount'
  ]
) {}
