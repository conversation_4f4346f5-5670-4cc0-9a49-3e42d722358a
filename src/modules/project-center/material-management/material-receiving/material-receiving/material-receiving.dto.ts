import { ApiProperty, PickType } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsBoolean,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString
} from 'class-validator';
import { pick } from 'lodash';

import {
  AuditStatus,
  MaterialSettlementStatus,
  PurchaseType,
  SubmitStatus
} from '@/prisma/generated';

export class BaseMaterialReceivingDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '采购结算状态' })
  @IsNotEmpty({ message: '采购结算状态不能为空' })
  @IsIn(Object.values(MaterialSettlementStatus), {
    message: '采购结算状态必须是有效枚举值'
  })
  @IsString({ message: '采购结算状态必须是字符串' })
  materialSettlementStatus: MaterialSettlementStatus;

  @ApiProperty({ description: '项目名称' })
  @IsOptional({ message: '项目名称可以为空' })
  @IsString({ message: '项目名称必须是字符串' })
  projectName?: string;

  @ApiProperty({ description: '单据编码' })
  @IsNotEmpty({ message: '单据编码不能为空' })
  @IsString({ message: '单据编码必须是字符串' })
  code: string;

  @ApiProperty({ description: '采购类型' })
  @IsOptional({ message: '采购类型可以为空' })
  @IsIn(Object.values(PurchaseType), {
    message: '采购类型必须是有效枚举值'
  })
  @IsString({ message: '采购类型必须是字符串' })
  purchaseType?: PurchaseType;

  @ApiProperty({ description: '供应商ID' })
  @IsOptional({ message: '供应商ID不能为空' })
  @IsString({ message: '供应商ID必须是字符串' })
  supplierId?: string;

  @ApiProperty({ description: '材料类别' })
  materialCategories: string;

  @ApiProperty({ description: '供应商名称' })
  @IsOptional({ message: '供应商名称不能为空' })
  @IsString({ message: '供应商名称必须是字符串' })
  supplierName?: string;

  @ApiProperty({ description: '合同ID' })
  @IsOptional({ message: '合同ID可以为空' })
  @IsString({ message: '合同ID必须是字符串' })
  contractId?: string;

  @ApiProperty({ description: '合同名称' })
  @IsOptional({ message: '合同名称可以为空' })
  @IsString({ message: '合同名称必须是字符串' })
  contractName?: string;

  @ApiProperty({ description: '不含税金额' })
  @IsOptional({ message: '不含税金额可以为空' })
  @IsNumber({}, { message: '不含税金额必须是数字' })
  taxExcludedAmount?: number;

  @ApiProperty({ description: '含税金额' })
  @IsOptional({ message: '含税金额可以为空' })
  @IsNumber({}, { message: '含税金额必须是数字' })
  taxIncludedAmount?: number;

  @ApiProperty({ description: '提交状态' })
  @IsNotEmpty({ message: '提交状态不能为空' })
  @IsIn(Object.values(SubmitStatus), {
    message: '提交状态必须是有效枚举值'
  })
  @IsString({ message: '提交状态必须是字符串' })
  submitStatus: SubmitStatus;

  @ApiProperty({ description: '审批状态' })
  @IsNotEmpty({ message: '审批状态不能为空' })
  @IsIn(Object.values(AuditStatus), {
    message: '审批状态必须是有效枚举值'
  })
  @IsString({ message: '审批状态必须是字符串' })
  auditStatus: AuditStatus;

  @ApiProperty({ description: '年' })
  @IsOptional({ message: '年可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '年必须是数字' })
  year?: number;

  @ApiProperty({ description: '月' })
  @IsOptional({ message: '月可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '月必须是数字' })
  month?: number;

  @ApiProperty({ description: '日' })
  @IsOptional({ message: '日可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '日必须是数字' })
  day?: number;

  @ApiProperty({ description: '创建人名称' })
  @IsNotEmpty({ message: '创建人名称不能为空' })
  @IsString({ message: '创建人名称必须是字符串' })
  creator: string;

  @ApiProperty({ description: '编制时间' })
  createAt: Date;
}

export class QueryMaterialReceivingDto extends PickType(
  BaseMaterialReceivingDto,
  ['year', 'month', 'day']
) {
  @ApiProperty({ description: '是否仅查看自己的数据,默认为false' })
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  onlyViewSelf: boolean;
}

export class QueryIncomingSupplierDto extends PickType(
  BaseMaterialReceivingDto,
  ['contractId']
) {}

export class QueryIncomingContractDto extends PickType(
  BaseMaterialReceivingDto,
  ['supplierId']
) {}

export class MaterialIncomingContractResDto extends PickType(
  BaseMaterialReceivingDto,
  ['contractId', 'contractName']
) {}

export class MaterialIncomingSupplierResDto extends PickType(
  BaseMaterialReceivingDto,
  ['supplierId', 'supplierName']
) {}

export class MaterialReceivingResDto extends BaseMaterialReceivingDto {}

// export class MaterialReceivingCreateDto extends PickType(
//   BaseMaterialReceivingDto,
//   [
//     'projectName',
//     'code',
//     'purchaseType',
//     'supplierId',
//     'supplierName',
//     'contractId',
//     'contractName',
//     'taxExcludedAmount',
//     'taxIncludedAmount',
//   ]
// ) {}

export class MaterialReceivingUpdateDto extends PickType(
  BaseMaterialReceivingDto,
  [
    'contractId',
    'contractName',
    'supplierId',
    'supplierName',
    'purchaseType',
    'year',
    'month',
    'day'
  ]
) {
  @ApiProperty({
    description: '收料单编码'
  })
  code: string;
}

export class MaterialReceivingUpdateSubmitStatusDto extends PickType(
  BaseMaterialReceivingDto,
  ['submitStatus']
) {}

export class MaterialReceivingUpdateAuditStatusDto extends PickType(
  BaseMaterialReceivingDto,
  ['auditStatus']
) {}
