import {
  BadRequestException,
  Injectable,
  NotFoundException
} from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import {
  ContractTemplateClassifyType,
  MaterialType,
  Prisma
} from '@/prisma/generated';
import { Decimal } from '@/prisma/generated/internal/prismaNamespace';

import { ContractConcreteSurchargeService } from '../contract-concrete-surcharge/contract-concrete-surcharge.service';
import {
  ContractDetailsListCreateDto,
  ContractDetailUpdateDto,
  EditMaterialDetailMoveDto,
  QueryChooseMateriaCategoryDto,
  QueryChooseMaterialDetailDto,
  QueryMaterialDetailDto
} from './contract-material-detail.dto';

@Injectable()
export class ContractMaterialDetailService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly contractConcreteSurchargeService: ContractConcreteSurchargeService
  ) {}

  async getMaterialCategory(
    reqUser: IReqUser,
    materialContractId: string,
    query: QueryChooseMateriaCategoryDto
  ) {
    const { tenantId, orgId } = reqUser;
    // 根据合同id查询合同范本所属的分类
    const contractTemplate = await this.getContractTemplate(
      reqUser,
      materialContractId
    );
    // 定义核算类型
    let materialType: MaterialType[] = [];
    if (contractTemplate.contractTemplate.classify === 'MATERIALS_PURCHASING') {
      // 材料-物资采购
      materialType = [
        MaterialType.CONSUME_MATERIAL,
        MaterialType.FIXEDASSETSL_CONSUMABLES,
        MaterialType.TURNOVERME_MATERIAL
      ];
    }
    if (
      contractTemplate.contractTemplate.classify ===
      'MATERIALS_COMMERCIAL_CONCRETE'
    ) {
      // 材料-商品混凝土
      materialType = [MaterialType.CONCRETE];
    }
    if (
      contractTemplate.contractTemplate.classify ===
      'MATERIALS_LEASING_TURNOVER'
    ) {
      // 材料-租赁周转材料
      materialType = [MaterialType.TURNOVERME_MATERIAL];
    }
    const searchTerm = `%${query.name}%`;
    // 获取成本核算已选的材料版本
    const materialVersion =
      await this.prisma.accountMaterialDictionaryVersion.findFirst({
        where: {
          tenantId,
          orgId,
          isDeleted: false
        },
        select: {
          versionId: true
        }
      });
    if (!materialVersion) {
      throw new NotFoundException('成本核算未设置材料版本');
    }
    return await this.prisma.$queryRaw<any[]>`
      with RECURSIVE tmp as (
        select 
          distinct 
          mdd.id, 
          mdd.parent_id, 
          mdd.material_dictionary_version_id, 
          mdd.code, 
          mdd."name", 
          mdd.type, 
          mdd.sort, 
          mdd.level, 
          mdd.remark
        from material_dictionary_category mdd 
        where mdd.is_deleted = false
        and mdd.tenant_id = ${tenantId}
        and mdd.is_active = true
        ${query.name ? Prisma.sql`and mdd.name like ${searchTerm}` : Prisma.empty}
        and mdd.material_dictionary_version_id = ${materialVersion.versionId}
        and mdd.id in (
          select mdd.material_dictionary_category_id from material_dictionary_detail mdd 
          where mdd.material_dictionary_version_id = ${materialVersion.versionId}
            and mdd.is_deleted = false
            and mdd.is_active = true
            and mdd.tenant_id = ${tenantId}
            and mdd.type::Text = ANY(${materialType}::text[])
        )
        
        UNION ALL
        
        select 
          distinct 
          mdd.id, 
          mdd.parent_id, 
          mdd.material_dictionary_version_id, 
          mdd.code, 
          mdd."name", 
          mdd.type, 
          mdd.sort, 
          mdd.level, 
          mdd.remark
        from material_dictionary_category mdd 
        join tmp t_m_p on t_m_p.parent_id = mdd.id
        where mdd.material_dictionary_version_id = ${materialVersion.versionId}
          and mdd.is_deleted = false
          and mdd.is_active = true
          and mdd.tenant_id = ${tenantId}
          ${query.name ? Prisma.sql`and mdd.name like ${searchTerm}` : Prisma.empty} 
      ) SELECT distinct id, parent_id, material_dictionary_version_id, code, name, type, sort, level, remark from tmp order by level, sort
    `;
  }

  async getMaterialDetail(reqUser: IReqUser, query: QueryMaterialDetailDto) {
    const { tenantId, orgId } = reqUser;
    const { materialContractId, materialDictionaryCategoryId, name } = query;
    // 根据合同id查询合同范本所属的分类
    const contractTemplate = await this.getContractTemplate(
      reqUser,
      materialContractId
    );
    // 定义核算类型
    let materialType: MaterialType[] = [];
    if (contractTemplate.contractTemplate.classify === 'MATERIALS_PURCHASING') {
      // 材料-物资采购
      materialType = [
        MaterialType.CONSUME_MATERIAL,
        MaterialType.FIXEDASSETSL_CONSUMABLES,
        MaterialType.TURNOVERME_MATERIAL
      ];
    }
    if (
      contractTemplate.contractTemplate.classify ===
      'MATERIALS_COMMERCIAL_CONCRETE'
    ) {
      // 材料-商品混凝土
      materialType = [MaterialType.CONCRETE];
    }
    if (
      contractTemplate.contractTemplate.classify ===
      'MATERIALS_LEASING_TURNOVER'
    ) {
      // 材料-租赁周转材料
      materialType = [MaterialType.TURNOVERME_MATERIAL];
    }
    // 获取成本核算已选的材料版本
    const materialVersion =
      await this.prisma.accountMaterialDictionaryVersion.findFirst({
        where: {
          tenantId,
          orgId,
          isDeleted: false
        },
        select: {
          versionId: true
        }
      });
    if (!materialVersion) {
      throw new NotFoundException('成本核算未设置材料版本');
    }
    const searchTerm = `%${name}%`;
    return await this.prisma.$queryRaw<any[]>`
      select 
        DISTINCT 
        mdd.id, 
        mdd.material_dictionary_category_id, 
        mdd.material_dictionary_version_id, 
        mdd.code, 
        mdd.name, 
        mdd.specification_model, 
        mdd.metering_unit, 
        mdd.remark, 
        mdd.type
      from material_dictionary_detail mdd 
      where mdd.material_dictionary_version_id = ${materialVersion.versionId}
        and mdd.is_deleted = false
        and mdd.is_active = true
        and mdd.tenant_id = ${tenantId}
        and mdd.type::text = ANY(${materialType}::text[])
        ${name ? Prisma.sql`and mdd.name like ${searchTerm}` : Prisma.empty}
        ${materialDictionaryCategoryId ? Prisma.sql`and mdd.material_dictionary_category_id = ${materialDictionaryCategoryId}` : Prisma.empty}
    `;
  }

  // 根据合同id查询合同范本所属的分类
  async getContractTemplate(reqUser: IReqUser, materialContractId: string) {
    const { tenantId, orgId } = reqUser;
    const contractTemplate = await this.prisma.materialContract.findUnique({
      where: {
        id: materialContractId,
        tenantId,
        orgId,
        isDeleted: false
      },
      select: {
        contractTemplate: {
          select: {
            classify: true
          }
        }
      }
    });
    if (!contractTemplate) {
      throw new BadRequestException('合同不存在');
    }
    return contractTemplate;
  }

  async add(reqUser: IReqUser, data: ContractDetailsListCreateDto) {
    const { orgId, tenantId, id: userId } = reqUser;
    const { list, materialContractId, contractTemplateType } = data;
    // 统一的数据处理逻辑
    const processedData = list.map((item) => ({
      ...item,
      materialContractId,
      tenantId,
      orgId,
      createBy: userId,
      updateBy: userId
    }));
    await this.prisma.$transaction(async (txPrisma) => {
      if (contractTemplateType === 'MATERIALS_PURCHASING') {
        await txPrisma.contractConsumeMaterialDetails.createMany({
          data: processedData
        });
      } else if (contractTemplateType === 'MATERIALS_COMMERCIAL_CONCRETE') {
        await txPrisma.contractConcreteDetails.createMany({
          data: processedData
        });
      } else if (contractTemplateType === 'MATERIALS_LEASING_TURNOVER') {
        await txPrisma.contractTurnoverMaterialDetails.createMany({
          data: processedData
        });
      } else {
        throw new Error(`不支持的合同模板类型: ${contractTemplateType}`);
      }
      // const materialDetailIds = list.map(
      //   (item) => item.materialDictionaryDetailId
      // );
      // // 初始化材料单位换算
      // await this.unitCalculationService.initAddDetail(
      //   txPrisma as PrismaService,
      //   materialContractId,
      //   reqUser,
      //   materialDetailIds
      // );
    });
    return true;
  }

  async getChooseMaterialDetail(
    reqUser: IReqUser,
    query: QueryChooseMaterialDetailDto,
    id: string
  ) {
    const { tenantId, orgId } = reqUser;
    const { contractTemplateType } = query;
    let res = [];
    if (contractTemplateType === 'MATERIALS_PURCHASING') {
      // 物资合同
      res = await this.prisma.$queryRaw<any[]>`
        select mdd.name, mdd.code, mdd.specification_model, ccmd.* from contract_consume_material_details ccmd
        join material_dictionary_detail mdd
          on mdd.id = ccmd.material_dictionary_detail_id
          and mdd.material_dictionary_version_id = ccmd.material_dictionary_version_id
          and mdd.material_dictionary_category_id = ccmd.material_dictionary_category_id
          and mdd.is_deleted = false
        where ccmd.material_contract_id = ${id}
          and ccmd.is_deleted = false
          and ccmd.tenant_id = ${tenantId}
          and ccmd.org_id = ${orgId}
        order by ccmd.sort asc
    `;
    }
    if (contractTemplateType === 'MATERIALS_COMMERCIAL_CONCRETE') {
      // 物资合同
      res = await this.prisma.$queryRaw<any[]>`
        select mdd.name, mdd.code, mdd.specification_model, ccmd.* from contract_concrete_details ccmd
        join material_dictionary_detail mdd
          on mdd.id = ccmd.material_dictionary_detail_id
          and mdd.material_dictionary_version_id = ccmd.material_dictionary_version_id
          and mdd.material_dictionary_category_id = ccmd.material_dictionary_category_id
          and mdd.is_deleted = false
        where ccmd.material_contract_id = ${id}
          and ccmd.is_deleted = false
          and ccmd.tenant_id = ${tenantId}
          and ccmd.org_id = ${orgId}
        order by ccmd.sort asc
    `;
    }
    if (contractTemplateType === 'MATERIALS_LEASING_TURNOVER') {
      // 物资合同
      res = await this.prisma.$queryRaw<any[]>`
        select mdd.name, mdd.code, mdd.specification_model, ccmd.* from contract_turnover_material_details ccmd
        join material_dictionary_detail mdd
          on mdd.id = ccmd.material_dictionary_detail_id
          and mdd.material_dictionary_version_id = ccmd.material_dictionary_version_id
          and mdd.material_dictionary_category_id = ccmd.material_dictionary_category_id
          and mdd.is_deleted = false
        where ccmd.material_contract_id = ${id}
          and ccmd.is_deleted = false
          and ccmd.tenant_id = ${tenantId}
          and ccmd.org_id = ${orgId}
        order by ccmd.sort asc
      `;
    }
    // 查询税率
    const taxRate = await this.getTaxRate(id, reqUser);
    return {
      taxRate,
      detailList: res
    };
  }

  /**
   * 查询税率
   * @param id 合同id
   */
  async getTaxRate(id: string, reqUser: IReqUser) {
    // 查询合同
    const contract = await this.prisma.materialContract.findUnique({
      where: {
        id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      }
    });
    const res = await this.prisma.$queryRaw<any[]>`
      select 
        ctfr."id", mcfr.decimal_value as value from field_rule fr
        join contract_template_field_rule ctfr
        on ctfr.field_rule_id = fr.id 
          and ctfr.contract_template_id = ${contract?.contractTemplateId}
          and ctfr.is_deleted = false
        join material_contract_field_rule mcfr
          on mcfr.contract_template_field_rule_id = ctfr."id"
          and mcfr.is_deleted = false
          and mcfr.org_id = ${reqUser.orgId}
          and mcfr.tenant_id = ${reqUser.tenantId}
          and mcfr.material_contract_id = ${id}
        where fr.code = '增值税税率' and fr.is_deleted = false
    `;
    if (res.length) {
      return res[0].value;
    } else {
      return null;
    }
  }

  async update(reqUser: IReqUser, id: string, data: ContractDetailUpdateDto) {
    const { id: userId } = reqUser;
    const { contractTemplateType, ...item } = data;
    if (contractTemplateType === 'MATERIALS_PURCHASING') {
      // 物资合同
      await this.prisma.contractConsumeMaterialDetails.update({
        where: {
          id: id,
          isDeleted: false
        },
        data: {
          ...item,
          updateBy: userId
        }
      });
    }
    if (contractTemplateType === 'MATERIALS_COMMERCIAL_CONCRETE') {
      // 商混合同
      await this.prisma.contractConcreteDetails.update({
        where: {
          id: id,
          isDeleted: false
        },
        data: {
          ...item,
          updateBy: userId
        }
      });
    }
    if (contractTemplateType === 'MATERIALS_LEASING_TURNOVER') {
      const {
        changeTotalValueAddedTaxAmount,
        changeTotalPriceExcludingTax,
        changeTotalPriceIncludingTax,
        ...res
      } = item;
      // 租赁周转合同
      await this.prisma.contractTurnoverMaterialDetails.update({
        where: {
          id: id,
          isDeleted: false
        },
        data: {
          ...res,
          updateBy: userId
        }
      });
    }
    return true;
  }

  async checkBeforeDel(
    id: string,
    reqUser: IReqUser,
    contractId: string,
    contractTemplateType: ContractTemplateClassifyType
  ) {
    const { tenantId, orgId } = reqUser;
    // 获取该明细的字典材料id
    let detail;
    if (contractTemplateType === 'MATERIALS_PURCHASING') {
      detail = await this.prisma.contractConsumeMaterialDetails.findUnique({
        select: {
          materialDictionaryDetailId: true
        },
        where: {
          id,
          tenantId,
          orgId,
          isDeleted: false
        }
      });
    }
    if (contractTemplateType === 'MATERIALS_COMMERCIAL_CONCRETE') {
      detail = await this.prisma.contractConcreteDetails.findUnique({
        select: {
          materialDictionaryDetailId: true
        },
        where: {
          id,
          tenantId,
          orgId,
          isDeleted: false
        }
      });
    }
    if (contractTemplateType === 'MATERIALS_LEASING_TURNOVER') {
      detail = await this.prisma.contractTurnoverMaterialDetails.findUnique({
        select: {
          materialDictionaryDetailId: true
        },
        where: {
          id,
          tenantId,
          orgId,
          isDeleted: false
        }
      });
    }
    if (!detail) {
      throw new NotFoundException('明细不存在');
    }
    const detailId = detail.materialDictionaryDetailId;
    // 查询该明细是否被引用
    const details = await this.prisma.$queryRaw<any[]>`
      select miid.id from material_incoming_inspection_detail miid
        join material_incoming_inspection mii
          on mii.id = miid.incoming_inspection_id
          and mii.tenant_id = miid.tenant_id
          and mii.org_id = miid.org_id
          and mii.is_deleted = false
        join material_contract mc 
          on mc.id = mii.contract_id
          and mc.tenant_id = mii.tenant_id
          and mc.org_id = mii.org_id
          and mc.is_deleted = false
          and mc.id = ${contractId}
      where miid.material_id = ${detailId}
        and miid.is_deleted = false
        and miid.tenant_id = ${tenantId}
        and miid.org_id = ${orgId}
    `;
    if (details.length) {
      throw new BadRequestException('该明细被引用，无法删除');
    }
    return detail;
  }

  async del(reqUser: IReqUser, id: string, contractId: string) {
    const { tenantId, orgId } = reqUser;
    // 查询模版的分类
    const contract = await this.prisma.materialContract.findUnique({
      where: {
        id: contractId,
        tenantId,
        orgId,
        isDeleted: false
      },
      select: {
        contractTemplate: {
          select: {
            classify: true
          }
        }
      }
    });
    const contractTemplateType = contract?.contractTemplate.classify;
    if (!contractTemplateType) {
      throw new BadRequestException('合同模板分类不存在');
    }
    // 校验明细是否被引用
    const detail = await this.checkBeforeDel(
      id,
      reqUser,
      contractId,
      contractTemplateType
    );
    // 查询该明细

    await this.prisma.$transaction(async (txPrisma) => {
      if (contractTemplateType === 'MATERIALS_PURCHASING') {
        // 物资采购
        await txPrisma.contractConsumeMaterialDetails.update({
          where: {
            id,
            tenantId,
            orgId,
            isDeleted: false
          },
          data: {
            isDeleted: true,
            updateBy: reqUser.id
          }
        });
      }
      if (contractTemplateType === 'MATERIALS_COMMERCIAL_CONCRETE') {
        // 材料-商品混凝土
        await txPrisma.contractConcreteDetails.update({
          where: {
            id,
            tenantId,
            orgId,
            isDeleted: false
          },
          data: {
            isDeleted: true,
            updateBy: reqUser.id
          }
        });
      }
      if (contractTemplateType === 'MATERIALS_LEASING_TURNOVER') {
        // 租赁周转材料
        // 材料-商品混凝土
        await txPrisma.contractTurnoverMaterialDetails.update({
          where: {
            id,
            tenantId,
            orgId,
            isDeleted: false
          },
          data: {
            isDeleted: true,
            updateBy: reqUser.id
          }
        });
      }
      // 单位换算
      await txPrisma.materialContractUnitCalculation.updateMany({
        where: {
          materialDetailId: detail.materialDictionaryDetailId,
          materialContractId: contractId,
          tenantId,
          orgId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
    });
    return true;
  }

  async addSupplementaryAgreement(
    txPrisma: PrismaService,
    reqUser: IReqUser,
    contractTemplateId: string,
    parentId: string,
    newContractId: string
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    // 查询合同模版的类型
    const contractTemplate = await this.prisma.contractTemplate.findUnique({
      where: {
        id: contractTemplateId
      },
      select: {
        classify: true
      }
    });
    // 查询父级下是否还有补充协议
    const hasSupplement = await this.prisma.materialContract.findFirst({
      select: {
        id: true
      },
      where: {
        parentId,
        tenantId,
        orgId,
        isDeleted: false
      },
      orderBy: {
        createAt: 'desc'
      }
    });
    if (hasSupplement) {
      // 有补充协议
      parentId = hasSupplement.id;
    }
    if (contractTemplate?.classify === 'MATERIALS_PURCHASING') {
      // 物资合同
      // 查询上级的合同明细
      const parentDetails =
        await this.prisma.contractConsumeMaterialDetails.findMany({
          where: {
            materialContractId: parentId,
            tenantId: tenantId,
            orgId: orgId,
            isDeleted: false
          }
        });
      // 给子级新增
      await txPrisma.contractConsumeMaterialDetails.createMany({
        data: parentDetails.map((item) => ({
          changeQuantity: item.changeQuantity,
          changePriceExcludingTax: item.changePriceExcludingTax,
          changePriceIncludingTax: item.changePriceIncludingTax,
          changeAddedTaxAmount: item.changeAddedTaxAmount,
          changeTotalPriceExcludingTax: item.changeTotalPriceExcludingTax,
          changeTotalValueAddedTaxAmount: item.changeTotalValueAddedTaxAmount,
          changeTotalPriceIncludingTax: item.changeTotalPriceIncludingTax,
          changeCalculateTotalPriceExcludingTax: 0,
          changeCalculateTotalValueAddedTaxAmount: 0,
          changeCalculateTotalPriceIncludingTax: 0,
          priceCalculate: 0,
          quantityCalculate: 0,
          changePercentage: '0',
          priceExcludingTax: item.changePriceExcludingTax,
          addedTaxAmount: item.changeAddedTaxAmount,
          priceIncludingTax: item.changePriceIncludingTax,
          provisionalQuantity: item.changeQuantity,
          totalPriceExcludingTax: item.changeTotalPriceExcludingTax,
          totalValueAddedTaxAmount: item.changeTotalValueAddedTaxAmount,
          totalPriceIncludingTax: item.changeTotalPriceIncludingTax,
          brand: item.brand,
          qualityStandard: item.qualityStandard,
          unit: item.unit,
          tenantId: item.tenantId,
          orgId: item.orgId,
          materialDictionaryVersionId: item.materialDictionaryVersionId,
          materialDictionaryCategoryId: item.materialDictionaryCategoryId,
          materialDictionaryDetailId: item.materialDictionaryDetailId,
          sourceMaterialContractId: parentId,
          materialContractId: newContractId,
          createBy: userId,
          updateBy: userId,
          remark: item.remark
        }))
      });
    }
    if (contractTemplate?.classify === 'MATERIALS_COMMERCIAL_CONCRETE') {
      // 商品混凝土
      const parentDetails = await this.prisma.contractConcreteDetails.findMany({
        where: {
          materialContractId: parentId,
          tenantId: tenantId,
          orgId: orgId,
          isDeleted: false
        }
      });
      // 给子级新增
      await txPrisma.contractConcreteDetails.createMany({
        data: parentDetails.map((item) => ({
          changeQuantity: item.changeQuantity,
          changePriceExcludingTax: item.changePriceExcludingTax,
          changePriceIncludingTax: item.changePriceIncludingTax,
          changeAddedTaxAmount: item.changeAddedTaxAmount,
          changeTotalPriceExcludingTax: item.changeTotalPriceExcludingTax,
          changeTotalValueAddedTaxAmount: item.changeTotalValueAddedTaxAmount,
          changeTotalPriceIncludingTax: item.changeTotalPriceIncludingTax,
          changeCalculateTotalPriceExcludingTax: 0,
          changeCalculateTotalValueAddedTaxAmount: 0,
          changeCalculateTotalPriceIncludingTax: 0,
          priceCalculate: 0,
          quantityCalculate: 0,
          changePercentage: '0',
          priceExcludingTax: item.changePriceExcludingTax,
          addedTaxAmount: item.changeAddedTaxAmount,
          priceIncludingTax: item.changePriceIncludingTax,
          provisionalQuantity: item.changeQuantity,
          totalPriceExcludingTax: item.changeTotalPriceExcludingTax,
          totalValueAddedTaxAmount: item.changeTotalValueAddedTaxAmount,
          totalPriceIncludingTax: item.changeTotalPriceIncludingTax,
          unit: item.unit,
          tenantId: item.tenantId,
          orgId: item.orgId,
          materialDictionaryVersionId: item.materialDictionaryVersionId,
          materialDictionaryCategoryId: item.materialDictionaryCategoryId,
          materialDictionaryDetailId: item.materialDictionaryDetailId,
          sourceMaterialContractId: parentId,
          materialContractId: newContractId,
          createBy: userId,
          updateBy: userId,
          remark: item.remark
        }))
      });
      // 查询上级的合同附加费
      const parentConcreteSurcharge =
        await this.prisma.contractConcreteSurcharge.findMany({
          select: {
            name: true,
            changePrice: true,
            remark: true
          },
          where: {
            materialContractId: parentId,
            orgId,
            tenantId
          }
        });
      await this.contractConcreteSurchargeService.init(
        txPrisma,
        reqUser,
        newContractId,
        parentConcreteSurcharge
      );
    }
    if (contractTemplate?.classify === 'MATERIALS_LEASING_TURNOVER') {
      // 租赁周转材料
      const parentDetails =
        await this.prisma.contractTurnoverMaterialDetails.findMany({
          where: {
            materialContractId: parentId,
            tenantId: tenantId,
            orgId: orgId,
            isDeleted: false
          }
        });
      // 给子级新增
      await txPrisma.contractTurnoverMaterialDetails.createMany({
        data: parentDetails.map((item) => ({
          priceExcludingTax: item.changePriceExcludingTax,
          addedTaxAmount: item.changeAddedTaxAmount,
          priceIncludingTax: item.changePriceIncludingTax,
          provisionalQuantity: item.changeQuantity,
          changeQuantity: item.changeQuantity,
          priceCalculate: 0,
          quantityCalculate: 0,
          changeProvisionalDays: item.changeProvisionalDays,
          provisionalDays: item.changeProvisionalDays,
          changeAddedTaxAmount: item.changeAddedTaxAmount,
          changePriceExcludingTax: item.changePriceExcludingTax,
          changePriceIncludingTax: item.changePriceIncludingTax,
          unit: item.unit,
          tenantId: item.tenantId,
          orgId: item.orgId,
          materialDictionaryVersionId: item.materialDictionaryVersionId,
          materialDictionaryCategoryId: item.materialDictionaryCategoryId,
          materialDictionaryDetailId: item.materialDictionaryDetailId,
          sourceMaterialContractId: parentId,
          materialContractId: newContractId,
          createBy: userId,
          updateBy: userId,
          remark: item.remark
        }))
      });
    }
  }

  // 上移
  async up(data: EditMaterialDetailMoveDto, reqUser: IReqUser) {
    // 查询当前明细的位置
    const { id, materialContractId, contractTemplateType } = data;
    const { tenantId, orgId } = reqUser;
    if (contractTemplateType === 'MATERIALS_PURCHASING') {
      // 物资采购
      const currentData =
        await this.prisma.contractConsumeMaterialDetails.findFirst({
          select: {
            id: true,
            sort: true
          },
          where: {
            id,
            tenantId: tenantId,
            orgId: orgId,
            isDeleted: false
          }
        });
      // 查询除本身之外的最大的排序
      const maxSortData =
        await this.prisma.contractConsumeMaterialDetails.findFirst({
          select: {
            id: true,
            sort: true
          },
          where: {
            sort: {
              lt: currentData?.sort
            },
            materialContractId,
            tenantId: tenantId,
            orgId: orgId,
            isDeleted: false
          },
          orderBy: {
            sort: 'desc'
          }
        });
      await this.prisma.$transaction(async (txPrisma) => {
        await txPrisma.contractConsumeMaterialDetails.update({
          where: {
            id,
            tenantId,
            orgId,
            isDeleted: false
          },
          data: {
            sort: maxSortData?.sort,
            updateBy: reqUser.id
          }
        });
        await txPrisma.contractConsumeMaterialDetails.update({
          where: {
            id: maxSortData?.id,
            tenantId,
            orgId,
            isDeleted: false
          },
          data: {
            sort: currentData?.sort,
            updateBy: reqUser.id
          }
        });
      });
    }
    if (contractTemplateType === 'MATERIALS_COMMERCIAL_CONCRETE') {
      // 材料-商品混凝土
      const currentData = await this.prisma.contractConcreteDetails.findFirst({
        select: {
          id: true,
          sort: true
        },
        where: {
          id,
          tenantId,
          orgId,
          isDeleted: false
        }
      });
      // 查询除本身之外的最大的排序
      const maxSortData = await this.prisma.contractConcreteDetails.findFirst({
        select: {
          id: true,
          sort: true
        },
        where: {
          materialContractId,
          tenantId,
          orgId,
          isDeleted: false,
          sort: {
            lt: currentData?.sort
          }
        },
        orderBy: {
          sort: 'desc'
        }
      });
      await this.prisma.$transaction(async (txPrisma) => {
        await txPrisma.contractConcreteDetails.update({
          where: {
            id,
            tenantId,
            orgId,
            isDeleted: false
          },
          data: {
            sort: maxSortData?.sort,
            updateBy: reqUser.id
          }
        });
        await txPrisma.contractConcreteDetails.update({
          where: {
            id: maxSortData?.id,
            tenantId,
            orgId,
            isDeleted: false
          },
          data: {
            sort: currentData?.sort,
            updateBy: reqUser.id
          }
        });
      });
    }
    if (contractTemplateType === 'MATERIALS_LEASING_TURNOVER') {
      // 租赁周转材料
      // 材料-商品混凝土
      const currentData =
        await this.prisma.contractTurnoverMaterialDetails.findFirst({
          select: {
            id: true,
            sort: true
          },
          where: {
            id,
            tenantId,
            orgId,
            isDeleted: false
          }
        });
      // 查询除本身之外的最大的排序
      const maxSortData =
        await this.prisma.contractTurnoverMaterialDetails.findFirst({
          select: {
            id: true,
            sort: true
          },
          where: {
            materialContractId,
            tenantId,
            orgId,
            isDeleted: false,
            sort: {
              lt: currentData?.sort
            }
          },
          orderBy: {
            sort: 'desc'
          }
        });
      await this.prisma.$transaction(async (txPrisma) => {
        await txPrisma.contractTurnoverMaterialDetails.update({
          where: {
            id,
            tenantId,
            orgId,
            isDeleted: false
          },
          data: {
            sort: maxSortData?.sort,
            updateBy: reqUser.id
          }
        });
        await txPrisma.contractTurnoverMaterialDetails.update({
          where: {
            id: maxSortData?.id,
            tenantId,
            orgId,
            isDeleted: false
          },
          data: {
            sort: currentData?.sort,
            updateBy: reqUser.id
          }
        });
      });
    }
    return true;
  }

  // 下移
  async down(data: EditMaterialDetailMoveDto, reqUser: IReqUser) {
    // 查询当前明细的位置
    const { id, materialContractId, contractTemplateType } = data;
    const { tenantId, orgId } = reqUser;
    if (contractTemplateType === 'MATERIALS_PURCHASING') {
      // 物资采购
      const currentData =
        await this.prisma.contractConsumeMaterialDetails.findFirst({
          select: {
            id: true,
            sort: true
          },
          where: {
            id,
            tenantId: tenantId,
            orgId: orgId,
            isDeleted: false
          }
        });
      // 查询除本身之外的最小的排序
      const maxSortData =
        await this.prisma.contractConsumeMaterialDetails.findFirst({
          select: {
            id: true,
            sort: true
          },
          where: {
            sort: {
              gt: currentData?.sort
            },
            materialContractId,
            tenantId: tenantId,
            orgId: orgId,
            isDeleted: false
          },
          orderBy: {
            sort: 'asc'
          }
        });
      await this.prisma.$transaction(async (txPrisma) => {
        await txPrisma.contractConsumeMaterialDetails.update({
          where: {
            id,
            tenantId,
            orgId,
            isDeleted: false
          },
          data: {
            sort: maxSortData?.sort,
            updateBy: reqUser.id
          }
        });
        await txPrisma.contractConsumeMaterialDetails.update({
          where: {
            id: maxSortData?.id,
            tenantId,
            orgId,
            isDeleted: false
          },
          data: {
            sort: currentData?.sort,
            updateBy: reqUser.id
          }
        });
      });
    }
    if (contractTemplateType === 'MATERIALS_COMMERCIAL_CONCRETE') {
      // 材料-商品混凝土
      const currentData = await this.prisma.contractConcreteDetails.findFirst({
        select: {
          id: true,
          sort: true
        },
        where: {
          id,
          tenantId,
          orgId,
          isDeleted: false
        }
      });
      // 查询除本身之外的最大的排序
      const maxSortData = await this.prisma.contractConcreteDetails.findFirst({
        select: {
          id: true,
          sort: true
        },
        where: {
          materialContractId,
          tenantId,
          orgId,
          isDeleted: false,
          sort: {
            gt: currentData?.sort
          }
        },
        orderBy: {
          sort: 'asc'
        }
      });
      await this.prisma.$transaction(async (txPrisma) => {
        await txPrisma.contractConcreteDetails.update({
          where: {
            id,
            tenantId,
            orgId,
            isDeleted: false
          },
          data: {
            sort: maxSortData?.sort,
            updateBy: reqUser.id
          }
        });
        await txPrisma.contractConcreteDetails.update({
          where: {
            id: maxSortData?.id,
            tenantId,
            orgId,
            isDeleted: false
          },
          data: {
            sort: currentData?.sort,
            updateBy: reqUser.id
          }
        });
      });
    }
    if (contractTemplateType === 'MATERIALS_LEASING_TURNOVER') {
      // 租赁周转材料
      // 材料-商品混凝土
      const currentData =
        await this.prisma.contractTurnoverMaterialDetails.findFirst({
          select: {
            id: true,
            sort: true
          },
          where: {
            id,
            tenantId,
            orgId,
            isDeleted: false
          }
        });
      // 查询除本身之外的最大的排序
      const maxSortData =
        await this.prisma.contractTurnoverMaterialDetails.findFirst({
          select: {
            id: true,
            sort: true
          },
          where: {
            materialContractId,
            tenantId,
            orgId,
            isDeleted: false,
            sort: {
              gt: currentData?.sort
            }
          },
          orderBy: {
            sort: 'asc'
          }
        });
      await this.prisma.$transaction(async (txPrisma) => {
        await txPrisma.contractTurnoverMaterialDetails.update({
          where: {
            id,
            tenantId,
            orgId,
            isDeleted: false
          },
          data: {
            sort: maxSortData?.sort,
            updateBy: reqUser.id
          }
        });
        await txPrisma.contractTurnoverMaterialDetails.update({
          where: {
            id: maxSortData?.id,
            tenantId,
            orgId,
            isDeleted: false
          },
          data: {
            sort: currentData?.sort,
            updateBy: reqUser.id
          }
        });
      });
    }
    return true;
  }

  async calculation(
    txPrisma: PrismaService,
    reqUser: IReqUser,
    id: string,
    contractTemplateId: string,
    value?: Decimal | null
  ) {
    if (value) {
      // 查询模版类型
      const template = await this.prisma.contractTemplate.findFirst({
        where: {
          id: contractTemplateId,
          isDeleted: false
        }
      });
      const taxRate = value;
      const contractTemplateType =
        template?.classify as ContractTemplateClassifyType;
      // 查询明细
      if (contractTemplateType === 'MATERIALS_PURCHASING') {
        const detail =
          await this.prisma.contractConsumeMaterialDetails.findMany({
            where: {
              orgId: reqUser.orgId,
              tenantId: reqUser.tenantId,
              materialContractId: id,
              isDeleted: false
            }
          });
        await Promise.all(
          detail.map(async (item) => {
            // 进行计算
            const obj = await this.calculatePrice(item, taxRate);

            await txPrisma.contractConsumeMaterialDetails.update({
              where: {
                id: item.id,
                isDeleted: false
              },
              data: {
                ...obj
              }
            });
          })
        );
      }
      if (contractTemplateType === 'MATERIALS_COMMERCIAL_CONCRETE') {
        const detail = await this.prisma.contractConcreteDetails.findMany({
          where: {
            orgId: reqUser.orgId,
            tenantId: reqUser.tenantId,
            materialContractId: id,
            isDeleted: false
          }
        });
        await Promise.all(
          detail.map(async (item) => {
            // 进行计算
            const obj = await this.calculatePrice(item, taxRate);

            await txPrisma.contractConcreteDetails.update({
              where: {
                id: item.id,
                isDeleted: false
              },
              data: {
                ...obj
              }
            });
          })
        );
      }
      if (contractTemplateType === 'MATERIALS_LEASING_TURNOVER') {
        const detail =
          await this.prisma.contractTurnoverMaterialDetails.findMany({
            where: {
              orgId: reqUser.orgId,
              tenantId: reqUser.tenantId,
              materialContractId: id,
              isDeleted: false
            }
          });
        await Promise.all(
          detail.map(async (item) => {
            // 进行计算
            const obj = await this.calculatePrice(item, taxRate);
            const {
              priceExcludingTax,
              addedTaxAmount,
              changePriceExcludingTax,
              changeAddedTaxAmount,
              priceCalculate,
              changePercentage,
              quantityCalculate
            } = obj;
            await txPrisma.contractTurnoverMaterialDetails.update({
              where: {
                id: item.id,
                isDeleted: false
              },
              data: {
                priceExcludingTax,
                addedTaxAmount,
                changePriceExcludingTax,
                changeAddedTaxAmount,
                priceCalculate,
                changePercentage,
                quantityCalculate
              }
            });
          })
        );
      }
    }
  }

  async calculatePrice(
    item: any,
    taxRate: Decimal
  ): Promise<{
    priceExcludingTax: Decimal | null;
    addedTaxAmount: Decimal | null;
    totalPriceExcludingTax: Decimal | null;
    totalValueAddedTaxAmount: Decimal | null;
    totalPriceIncludingTax: Decimal | null;
    changePriceExcludingTax: Decimal | null;
    changeAddedTaxAmount: Decimal | null;
    changeTotalPriceExcludingTax: Decimal | null;
    changeTotalValueAddedTaxAmount: Decimal | null;
    changeTotalPriceIncludingTax: Decimal | null;
    changeCalculateTotalPriceExcludingTax: Decimal | null;
    changeCalculateTotalValueAddedTaxAmount: Decimal | null;
    changeCalculateTotalPriceIncludingTax: Decimal | null;
    priceCalculate: Decimal | null;
    changePercentage: string | null;
    quantityCalculate: Decimal | null;
  }> {
    // 变更前
    // 原不含税单价
    const priceExcludingTax = item.priceIncludingTax
      ? Decimal(item.priceIncludingTax).div(taxRate.plus(1))
      : null;

    // 原增值税额
    const addedTaxAmount =
      item.priceIncludingTax && priceExcludingTax
        ? Decimal(item.priceIncludingTax).minus(priceExcludingTax)
        : null;

    // 变更前不含税总价
    const totalPriceExcludingTax =
      item.provisionalQuantity && priceExcludingTax
        ? Decimal(priceExcludingTax).times(item.provisionalQuantity)
        : null;

    // 变更前增值税总额
    const totalValueAddedTaxAmount =
      item.provisionalQuantity && addedTaxAmount
        ? Decimal(addedTaxAmount).times(item.provisionalQuantity)
        : null;

    // 变更前含税总价
    const totalPriceIncludingTax =
      item.provisionalQuantity && item.priceIncludingTax
        ? Decimal(item.priceIncludingTax).times(item.provisionalQuantity)
        : null;

    // 变更后
    // 不含税单价
    const changePriceExcludingTax = item.changePriceIncludingTax
      ? Decimal(item.changePriceIncludingTax).div(taxRate.plus(1))
      : null;

    // 增值税额
    const changeAddedTaxAmount =
      item.changePriceIncludingTax && changePriceExcludingTax
        ? Decimal(item.changePriceIncludingTax).minus(changePriceExcludingTax)
        : null;

    // 不含税总价
    const changeTotalPriceExcludingTax =
      item.changeQuantity && changePriceExcludingTax
        ? Decimal(changePriceExcludingTax).times(item.changeQuantity)
        : null;

    // 增值税总额
    const changeTotalValueAddedTaxAmount =
      item.changeQuantity && changeAddedTaxAmount
        ? Decimal(changeAddedTaxAmount).times(item.changeQuantity)
        : null;

    // 含税总价
    const changeTotalPriceIncludingTax =
      item.changePriceIncludingTax && item.changeQuantity
        ? Decimal(item.changePriceIncludingTax).times(item.changeQuantity)
        : null;

    // 变更增减金额[不含税总价]
    const changeCalculateTotalPriceExcludingTax =
      changeTotalPriceExcludingTax && totalPriceExcludingTax
        ? Decimal(changeTotalPriceExcludingTax).minus(totalPriceExcludingTax)
        : null;

    // 变更增减增[增值税总额]
    const changeCalculateTotalValueAddedTaxAmount =
      changeTotalValueAddedTaxAmount && totalValueAddedTaxAmount
        ? Decimal(changeTotalValueAddedTaxAmount).minus(
            totalValueAddedTaxAmount
          )
        : null;

    // 变更增减[含税总价]
    const changeCalculateTotalPriceIncludingTax =
      changeTotalPriceIncludingTax && totalPriceIncludingTax
        ? Decimal(changeTotalPriceIncludingTax).minus(totalPriceIncludingTax)
        : null;

    // 变更价格增减
    const priceCalculate =
      item.changePriceIncludingTax && item.priceIncludingTax
        ? Decimal(item.changePriceIncludingTax).minus(item.priceIncludingTax)
        : null;

    // 变更数量增减
    const quantityCalculate =
      item.provisionalQuantity && item.changeQuantity
        ? Decimal(item.changeQuantity).minus(item.provisionalQuantity)
        : null;

    // 变更增减百分比率
    const changePercentage =
      changeCalculateTotalPriceExcludingTax && totalPriceExcludingTax
        ? Decimal(changeCalculateTotalPriceExcludingTax)
            .div(totalPriceExcludingTax)
            .mul(100)
            .toString()
        : null;

    return {
      priceExcludingTax,
      addedTaxAmount,
      totalPriceExcludingTax,
      totalValueAddedTaxAmount,
      totalPriceIncludingTax,
      changePriceExcludingTax,
      changeAddedTaxAmount,
      changeTotalPriceExcludingTax,
      changeTotalValueAddedTaxAmount,
      changeTotalPriceIncludingTax,
      changeCalculateTotalPriceExcludingTax,
      changeCalculateTotalValueAddedTaxAmount,
      changeCalculateTotalPriceIncludingTax,
      priceCalculate,
      changePercentage,
      quantityCalculate
    };
  }
}
