import {
  BadRequestException,
  Injectable,
  NotFoundException
} from '@nestjs/common';
import * as dayjs from 'dayjs';
import * as uuid from 'uuid';

import { CommonRepositories } from '@/common/common-repositories';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { DetailType, MaterialReturnSalesForm } from '@/prisma/generated';
import { Decimal } from '@/prisma/generated/internal/prismaNamespace';

import {
  MaterialReturnSalesFormChooseCategoryTreeResDto,
  MaterialReturnSalesFormChooseDetailsDto,
  MaterialReturnSalesFormChooseDetailsQueryDto,
  MaterialReturnSalesFormChooseDetailsResDto,
  MaterialReturnSalesFormDetailCreateDto,
  MaterialReturnSalesFormDetailCreateListDto,
  MaterialReturnSalesFormDetailUpdateDto
} from './material-return-sales-form-detail.dto';
import { MaterialReturnSalesFormDetailRepository } from './material-return-sales-form-detail.repositories';

interface Material {
  id: string;
  materialId: string;
  materialName: string;
  materialSpec: string;
  unit: string;
  price: number;
  inventoryQuantity: number;
}

interface MaterialRecord {
  inventoryId: string;
  unit: string;
  price: number;
  quantity: number;
  code: string;
  materialId: string;
  materialName: string;
  materialSpec: string;
  createAt: string;
}

@Injectable()
export class MaterialReturnSalesFormDetailService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: MaterialReturnSalesFormDetailRepository
  ) {}

  /**
   * 获取退货单下所有明细（子级）
   * @param id
   * @param reqUser
   * @returns
   */
  async getList(id: string, reqUser: IReqUser) {
    return await this.prisma.materialReturnSalesFormDetail.findMany({
      where: {
        parentId: null,
        returnSalesFormId: id,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });
  }

  /**
   * 查询可选的材料分类
   * @param returnSalesFormId
   * @param reqUser
   */
  async getChooseMaterialCategory(
    returnSalesFormId: string,
    reqUser: IReqUser
  ): Promise<MaterialReturnSalesFormChooseCategoryTreeResDto[]> {
    // 根据退货单id查询该退货单
    const returnSalesForm = await this.getOne(returnSalesFormId, reqUser);
    const materialCategoryList: MaterialReturnSalesFormChooseCategoryTreeResDto[] =
      await this.repository.getChooseMaterialCategory(
        returnSalesForm?.contractId,
        returnSalesForm?.supplierId,
        reqUser
      );
    return materialCategoryList;
  }

  /**
   * 查询可选的材料明细
   * @param returnSalesFormId
   * @param reqUser
   */
  async getChooseMaterialDetails(
    query: MaterialReturnSalesFormChooseDetailsQueryDto,
    reqUser: IReqUser
  ): Promise<MaterialReturnSalesFormChooseDetailsResDto[]> {
    // 根据退货单id查询该退货单
    const returnSalesForm = await this.getOne(query.returnSalesFormId, reqUser);
    const materialDetailsList: MaterialReturnSalesFormChooseDetailsDto[] =
      await this.repository.getChooseMaterialDetails(
        query.returnSalesFormId,
        returnSalesForm?.contractId,
        returnSalesForm?.supplierId,
        reqUser,
        query.categoryId
      );
    return await this.mergeMaterials(materialDetailsList);
  }

  async add(
    body: MaterialReturnSalesFormDetailCreateDto,
    reqUser: IReqUser
  ): Promise<any> {
    const { returnSalesFormId, list } = body;
    const { id: userId } = reqUser;
    const addList: any[] = [];
    const updateList: any[] = [];
    // 根据退货单id查询该退货单
    const returnSalesForm = await this.getOne(returnSalesFormId, reqUser);
    // 查询所有涉及到的材料的收料
    const receivingAndReturnList =
      await this.repository.getMaterialReceivingAndReturnList(
        reqUser,
        list,
        returnSalesForm?.contractId,
        returnSalesForm?.supplierId
      );
    // 将数据进行map重组再排序
    const materialMap = await this.groupAndSortMaterials(
      receivingAndReturnList,
      list
    );
    // 判断父级是否存在
    for (const element of materialMap.keys()) {
      const obj = await this.notHasParent(
        reqUser,
        returnSalesFormId,
        element,
        materialMap.get(element) || []
      );
      // 数据计算
      const list = await this.calculation(obj.addList);
      addList.push(...list);
      updateList.push(...obj.updateList);
    }
    await this.prisma.$transaction(async (tx) => {
      // 新增数据
      await tx.materialReturnSalesFormDetail.createMany({
        data: addList
      });
      // 修改库存
      for (const element of updateList) {
        await tx.materialReceivingInventory.update({
          where: {
            id: element.id,
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId
          },
          data: {
            inventoryQuantity: {
              decrement: element.quantity
            },
            createBy: userId,
            updateBy: userId
          }
        });
      }
    });
    // 计算并统计金额写入单据
    // 查询单据下所有的明细
    const receivingAmount =
      await this.prisma.materialReturnSalesFormDetail.aggregate({
        where: {
          returnSalesFormId,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          isDeleted: false,
          parentId: null
        },
        _sum: {
          salesReturnAmount: true
        }
      });
    await this.prisma.materialReturnSalesForm.update({
      where: {
        id: returnSalesFormId,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      data: {
        amount: receivingAmount._sum.salesReturnAmount
      }
    });
    return true;
  }

  async calculation(addList: any[]) {
    const list: any[] = [];
    const parent: any = addList.find(
      (element) => element.parentId === null || !element.parentId
    );
    const childList = addList.filter((element) => {
      return element.parentId;
    });
    // 同批总金额
    const totalAmount = childList.reduce(
      (pre: Decimal, cur) =>
        pre.add(Decimal(cur.salesReturnPrice).mul(cur.salesReturnQuantity)),
      new Decimal(0)
    );
    // 计算子级退货数量之和
    const totalQuantity = childList.reduce(
      (pre: Decimal, cur) => pre.add(Decimal(cur.salesReturnQuantity)),
      new Decimal(0)
    );
    // 如果子级退货数量相加等于父级
    let notLastAmount = new Decimal(0);
    if (parent.salesReturnQuantity === totalQuantity.toNumber()) {
      // 说明存在最后一批退货
      // 计算除最后一批退货的金额
      childList.forEach((item, index) => {
        if (index !== 0) {
          notLastAmount = notLastAmount.add(
            Decimal(item.salesReturnPrice).mul(item.salesReturnQuantity)
          );
        }
      });
    }
    // 最后一批的金额
    childList.forEach((element, index) => {
      if (index === 0) {
        // 最后一批
        element.salesReturnPrice = element.inStockPrice;
        element.salesReturnAmount = Decimal(totalAmount.sub(notLastAmount));
      } else {
        // 不是最后一批
        element.salesReturnPrice = element.inStockPrice;
        element.salesReturnAmount = Decimal(element.salesReturnPrice).mul(
          element.salesReturnQuantity
        );
      }
      list.push(element);
    });
    // 计算父级
    parent.salesReturnAmount = list.reduce(
      (pre, cur) => pre.add(cur.salesReturnAmount),
      new Decimal(0)
    );
    parent.salesReturnPrice = parent.salesReturnAmount.div(
      parent.salesReturnQuantity
    );
    list.unshift(parent);
    return list;
  }

  async update(
    id: string,
    body: MaterialReturnSalesFormDetailUpdateDto,
    reqUser: IReqUser
  ) {
    // 校验明细
    const detail = await this.prisma.materialReturnSalesFormDetail.findUnique({
      where: {
        id,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });
    if (!detail) {
      throw new NotFoundException('明细不存在');
    }
    if (
      Decimal(body.salesReturnQuantity).equals(detail.salesReturnQuantity || 0)
    ) {
      // 如果数据一致，则不处理，只更新备注
      await this.prisma.materialReturnSalesFormDetail.update({
        where: {
          id: id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        },
        data: {
          remark: body.remark
        }
      });
      return true;
    }
    // 查询单据
    const returnSalesForm = await this.getOne(
      detail.returnSalesFormId,
      reqUser
    );
    const param: any = {
      contractId: returnSalesForm?.contractId,
      supplierId: returnSalesForm?.supplierId,
      materialName: detail.materialName,
      materialSpec: detail.materialSpec,
      unit: detail.unit,
      price: detail.inStockPrice
    };
    // 校验退货数量
    await this.checkReturnQuantity(
      param,
      Decimal(body.salesReturnQuantity),
      detail.salesReturnQuantity || null,
      reqUser
    );
    // 删除现有的明细
    await this.delete(id, reqUser);
    // 查询现有库存数量
    const inStockQuantity = await this.repository.getInventory(param, reqUser);
    const inStockPrice = detail.inStockPrice
      ? Decimal(detail.inStockPrice).toNumber()
      : 0;
    // 重新创建
    const materialReturnSalesFormDetailCreateDto: MaterialReturnSalesFormDetailCreateDto =
      {
        list: [
          {
            materialId: detail.materialId,
            materialName: detail.materialName,
            materialSpec: detail.materialSpec,
            unit: detail.unit || '',
            inStockQuantity,
            inStockPrice,
            salesReturnQuantity: body.salesReturnQuantity,
            remark: body.remark,
            orderNo: detail.orderNo
          }
        ],
        returnSalesFormId: detail.returnSalesFormId
      };
    await this.add(materialReturnSalesFormDetailCreateDto, reqUser);
    return true;
  }

  /**
   * 校验退货数量
   */
  async checkReturnQuantity(
    data: any,
    salesReturnQuantity: Decimal,
    parentSalesReturnQuantity: Decimal | null,
    reqUser: IReqUser
  ) {
    // 查询现有库存数量
    const inventoryQuantity = Decimal(
      await this.repository.getInventory(data, reqUser)
    );
    // 该退货单明细现存退货数量
    const existingSalesReturnQuantity = Decimal(parentSalesReturnQuantity || 0);
    // 要变更的库存
    const updateSalesReturnQuantity = Decimal(salesReturnQuantity);
    if (existingSalesReturnQuantity < updateSalesReturnQuantity) {
      // 要补的退货数量
      const addSalesReturnQuantity = updateSalesReturnQuantity.minus(
        existingSalesReturnQuantity
      );
      if (inventoryQuantity < addSalesReturnQuantity) {
        // 现有库存不足，无法退
        throw new BadRequestException('当前库存数量不足，请重新选择退货数量');
      }
    }
  }

  async delete(id: string, reqUser: IReqUser) {
    // 校验父级
    await this.getOneDetail(id, reqUser);
    // 查询改明细下的所有子级
    const children = await this.getDetailChildren(id, reqUser);
    // 数据调整
    const changeChildren = await this.getDetailChildrenList(children);
    const ids: string[] = [];
    children.forEach((item) => {
      ids.push(item.id);
    });
    ids.push(id);
    // 然后进行库存返回
    await this.prisma.$transaction(async (tx) => {
      for (const element of changeChildren) {
        await tx.materialReceivingInventory.update({
          where: {
            id: element.materialReceivingInventoryId || ''
          },
          data: {
            inventoryQuantity: {
              increment: Decimal(element.salesReturnQuantity || 0)
            },
            updateBy: reqUser.id
          }
        });
      }
      await tx.materialReturnSalesFormDetail.updateMany({
        where: {
          id: {
            in: ids
          }
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
    });
    return true;
  }

  async getDetailList(id: string, reqUser: IReqUser) {
    const res = await this.prisma.materialReturnSalesFormDetail.findMany({
      select: {
        id: true,
        returnSalesFormId: true,
        parentId: true,
        detailCode: true,
        detailDate: true,
        materialId: true,
        materialName: true,
        materialSpec: true,
        unit: true,
        orderNo: true,
        inStockQuantity: true,
        inStockPrice: true,
        salesReturnQuantity: true,
        salesReturnPrice: true,
        salesReturnAmount: true,
        remark: true
      },
      where: {
        returnSalesFormId: id,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      },
      orderBy: {
        orderNo: 'asc'
      }
    });
    return res.map((item) => {
      if (item.parentId) {
        item.materialName = item.detailCode || '';
        item.materialSpec = item.detailDate
          ? dayjs(item.detailDate).format('YYYY-MM-DD')
          : '';
      }
      return {
        ...item
      };
    });
  }

  // 查询退货单下收料明细的最早时间
  async getEarliestTime(
    supplierId: string,
    contractId: string,
    reqUser: IReqUser
  ): Promise<string | null> {
    return await this.repository.getEarliestTime(
      supplierId,
      contractId,
      reqUser
    );
  }

  async move(reqUser: IReqUser, fromId: string, toId: string) {
    await CommonRepositories.changeDataOrderNo(this.prisma, {
      tenantId: reqUser.tenantId,
      orgId: reqUser.orgId,
      fromId,
      toId,
      tableName: 'material_return_sales_form_detail'
    });
    return true;
  }

  async getDetailChildrenList(children: any[]) {
    return children.map((item) => {
      if (item.detailType === DetailType.RECEIVING) {
        // 查看是否存在相应退库数量
        const target = children.find(
          (element) =>
            element.detailType === DetailType.RETURN_INVENTORY &&
            element.materialReceivingInventoryId ===
              item.materialReceivingInventoryId
        );
        if (target) {
          item.salesReturnQuantity += target?.salesReturnQuantity || 0;
        }
      }
      return {
        ...item
      };
    });
  }

  /**
   * 父级不存在
   * @param data 数据
   * @param reqUser
   * @param returnSalesFormId 退货单id
   */
  async notHasParent(
    reqUser: IReqUser,
    returnSalesFormId: string,
    materialData: MaterialReturnSalesFormDetailCreateListDto,
    data: MaterialRecord[]
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    const parentId = uuid.v7();
    // 新增的退货数组
    const addList = [];
    // 编辑库存的数组
    const updateList = [];
    // 创建父级
    addList.push({
      returnSalesFormId,
      id: parentId,
      orgId,
      tenantId,
      materialId: materialData.materialId,
      materialName: materialData.materialName,
      materialSpec: materialData.materialSpec,
      unit: materialData.unit,
      inStockQuantity: materialData.inStockQuantity,
      inStockPrice: materialData.inStockPrice,
      salesReturnQuantity: materialData.salesReturnQuantity,
      createBy: userId,
      updateBy: userId,
      remark: materialData.remark,
      orderNo: materialData.orderNo
    });
    // 要退的数量
    let quantity = materialData.salesReturnQuantity || 0;
    // 判断子级
    // 将退库的数量在收料里面减去
    // data = await this.processInventoryQuantities(data);
    for (const element of data) {
      // 要退的数量必须要大于0，小于0后停止
      if (quantity > 0) {
        // 先判断应该取退库单数据还是收料单数据
        // 在库库存大于退库单库存，取退库单数据
        addList.push({
          orgId,
          tenantId,
          createBy: userId,
          updateBy: userId,
          parentId,
          returnSalesFormId,
          detailCode: element.code,
          detailDate: new Date(element.createAt),
          materialId: element.materialId,
          materialName: element.materialName,
          materialReceivingInventoryId: element.inventoryId,
          materialSpec: element.materialSpec,
          unit: element.unit,
          inStockQuantity: element.quantity,
          inStockPrice: element.price,
          salesReturnQuantity:
            quantity > element.quantity ? element.quantity : quantity,
          salesReturnPrice: element.price,
          salesReturnAmount: Decimal(element.price).mul(
            quantity > element.quantity ? element.quantity : quantity
          )
        });
        updateList.push({
          id: element.inventoryId,
          quantity: quantity > element.quantity ? element.quantity : quantity
        });
        quantity -= element.quantity;
      }
    }
    return {
      addList,
      updateList
    };
  }

  // 数据转换，将存在退库单的收料库存减去退库的库存
  // async processInventoryQuantities(records: MaterialRecord[]) {
  //   // 按 inventoryId 分组
  //   const inventoryGroups = new Map<string, MaterialRecord[]>();

  //   records.forEach((record) => {
  //     if (!inventoryGroups.has(record.inventoryId)) {
  //       inventoryGroups.set(record.inventoryId, []);
  //     }
  //     inventoryGroups.get(record.inventoryId)!.push(record);
  //   });

  //   // 处理每个分组
  //   inventoryGroups.forEach((group) => {
  //     // 分别计算 RECEIVING 和 INVENTORY 的总 quantity
  //     const receivingRecords = group.filter(
  //       (r) => r.detailType === 'RECEIVING'
  //     );
  //     const inventoryRecords = group.filter(
  //       (r) => r.detailType === 'INVENTORY'
  //     );

  //     if (receivingRecords.length > 0 && inventoryRecords.length > 0) {
  //       // 计算总差值
  //       const totalReceiving = receivingRecords.reduce(
  //         (sum, r) => sum + r.quantity,
  //         0
  //       );
  //       const totalInventory = inventoryRecords.reduce(
  //         (sum, r) => sum + r.quantity,
  //         0
  //       );
  //       const difference = totalReceiving - totalInventory;

  //       // 更新最后一条 RECEIVING 记录的 quantity
  //       const lastReceivingRecord =
  //         receivingRecords[receivingRecords.length - 1];
  //       lastReceivingRecord.quantity = difference;
  //     }
  //   });

  //   return records;
  // }

  // 将数据按照材料名称+材料规格+材料单价进行map重组再排序
  async groupAndSortMaterials(
    records: MaterialRecord[],
    parent: MaterialReturnSalesFormDetailCreateListDto[]
  ): Promise<
    Map<MaterialReturnSalesFormDetailCreateListDto, MaterialRecord[]>
  > {
    // 创建一个映射表，键为 materialName、materialSpec 和 price 的组合
    const materialMap = new Map<
      MaterialReturnSalesFormDetailCreateListDto,
      MaterialRecord[]
    >();

    parent.forEach((item) => {
      materialMap.set(item, []);
    });

    // 按 materialName、materialSpec 和 price 分组
    for (const element of materialMap.keys()) {
      records.forEach((record) => {
        if (
          element.materialId === record.materialId &&
          element.materialName === record.materialName &&
          element.materialSpec === record.materialSpec &&
          element.unit === record.unit &&
          element.inStockPrice === record.price
        ) {
          materialMap.get(element)!.push(record);
        }
      });
    }

    // 对每个分组内的记录进行排序
    materialMap.forEach((group) => {
      group.sort((a, b) => {
        // 先比较 createAt（降序）
        const dateA = new Date(a.createAt);
        const dateB = new Date(b.createAt);
        const dateDiff = dateB.getTime() - dateA.getTime();
        return dateDiff; // 日期不同时，按日期降序
      });
    });

    return materialMap;
  }

  // 查询所有的父级明细
  async getParentList(returnSalesFormId: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    return await this.prisma.materialReturnSalesFormDetail.findMany({
      where: {
        returnSalesFormId,
        parentId: null,
        tenantId,
        orgId,
        isDeleted: false
      }
    });
  }

  // 数据合并
  private async mergeMaterials(
    materials: MaterialReturnSalesFormChooseDetailsDto[]
  ): Promise<Material[]> {
    // 创建一个映射表，键为组合的唯一标识，值为合并后的材料对象
    const mergedMap = new Map<string, Material>();

    materials.forEach((material) => {
      // 创建基于 material_id、material_name、material_spec、unit 和 price 的唯一键
      const key = `${material.materialId}-${material.materialName}-${material.materialSpec}-${material.unit}-${material.price}`;

      if (mergedMap.has(key)) {
        // 如果已存在相同组合的材料，则累加 inventory_quantity
        const existingMaterial = mergedMap.get(key)!;
        existingMaterial.inventoryQuantity += material.inventoryQuantity;
      } else {
        // 如果不存在，则添加新的材料记录
        mergedMap.set(key, { ...material });
      }
    });

    // 将映射表中的值转换为数组返回
    return Array.from(mergedMap.values());
  }

  /**
   * 查询单个退货单
   * @param id
   * @param reqUser
   * @returns
   */
  async getOne(
    id: string,
    reqUser: IReqUser
  ): Promise<MaterialReturnSalesForm> {
    const returnSalesForm =
      await this.prisma.materialReturnSalesForm.findUnique({
        where: {
          id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        }
      });
    if (!returnSalesForm) {
      throw new NotFoundException('未找到该退货单');
    }
    return returnSalesForm;
  }

  /**
   * 查询单个退货单详情
   * @param id
   * @param reqUser
   * @returns
   */
  async getOneDetail(id: string, reqUser: IReqUser) {
    const returnSalesFormDetail =
      await this.prisma.materialReturnSalesFormDetail.findUnique({
        where: {
          id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        }
      });
    if (!returnSalesFormDetail) {
      throw new NotFoundException('未找到该退货单明细');
    }
    if (returnSalesFormDetail.parentId) {
      throw new BadRequestException(`只有父级可以操作`);
    }
    return returnSalesFormDetail;
  }

  /**
   * 查询父级明细下的所有子级
   * @param id
   * @param reqUser
   * @returns
   */
  async getDetailChildren(id: string, reqUser: IReqUser) {
    return await this.prisma.materialReturnSalesFormDetail.findMany({
      where: {
        parentId: id,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });
  }
}
