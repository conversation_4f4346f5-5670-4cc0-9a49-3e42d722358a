import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { TimeListResponseDto } from '@/common/dtos/common.dto';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { MaterialReturnInventoryForm } from '@/prisma/generated';

import {
  MaterialReturnInventoryAttachmentCreateDto,
  MaterialReturnInventoryAttachmentResDto,
  MaterialReturnInventoryFormChooseCategoryQueryDto,
  MaterialReturnInventoryFormChooseCategoryTreeResDto,
  MaterialReturnInventoryFormChooseDetailsQueryDto,
  MaterialReturnInventoryFormChooseDetailsResDto,
  MaterialReturnInventoryFormCreateDto,
  MaterialReturnInventoryFormResDto,
  MaterialReturnInventoryFormSupplierResDto,
  MaterialReturnInventoryFormUpdateAuditStatusDto,
  MaterialReturnInventoryFormUpdateDto,
  MaterialReturnInventoryFormUpdateSubmitStatusDto,
  QueryMaterialReturnInventoryFormDto
} from './material-return-inventory-form.dto';
import { ReturnInventoryFormService } from './services/return-inventory-form.service';
import { ReturnInventoryFormAttachmentService } from './services/return-inventory-form-attachment.service';
import { ReturnInventoryFormDetailService } from './services/return-inventory-form-detail.service';

@ApiTags('退库单')
@Controller('return-inventory-form')
export class MaterialReturnInventoryFormController {
  constructor(
    private readonly service: ReturnInventoryFormAttachmentService,
    private readonly returnInventoryFormService: ReturnInventoryFormService,
    private readonly returnInventoryFormDetailService: ReturnInventoryFormDetailService
  ) {}

  @ApiOperation({ summary: '获取领料单下的供应商列表' })
  @ApiResponse({
    status: 200,
    description: '获取领料单下的供应商列表成功',
    type: MaterialReturnInventoryFormSupplierResDto,
    isArray: true
  })
  @Get('/supplier-list')
  async getSupplierList(
    @ReqUser() reqUser: IReqUser
  ): Promise<MaterialReturnInventoryFormSupplierResDto[]> {
    return await this.returnInventoryFormService.getSupplierList(reqUser);
  }

  @ApiOperation({ summary: '新增退库单' })
  @ApiResponse({
    status: 200,
    description: '新增退库单成功'
  })
  @Post('/form')
  async addForm(
    @ReqUser() reqUser: IReqUser,
    @Body() data: MaterialReturnInventoryFormCreateDto
  ): Promise<MaterialReturnInventoryForm> {
    return await this.returnInventoryFormService.add(reqUser, data);
  }

  @ApiOperation({ summary: '编辑退库单' })
  @ApiResponse({
    status: 200,
    description: '编辑退库单成功'
  })
  @Patch('/form/:id')
  async update(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: MaterialReturnInventoryFormUpdateDto
  ): Promise<boolean> {
    return await this.returnInventoryFormService.update(id, reqUser, data);
  }

  @ApiOperation({ summary: '退库单左侧树' })
  @ApiResponse({
    status: 200,
    description: '获取退库单左侧树成功',
    type: TimeListResponseDto,
    isArray: true
  })
  @Get('/date-tree')
  async getDateTree(@ReqUser() reqUser: IReqUser) {
    return await this.returnInventoryFormService.getDateTree(reqUser);
  }

  @ApiOperation({ summary: '退库单列表' })
  @ApiResponse({
    status: 200,
    description: '获取退库单列表成功',
    type: MaterialReturnInventoryFormResDto,
    isArray: true
  })
  @Get('/form')
  async getFormList(
    @ReqUser() reqUser: IReqUser,
    @Query() query: QueryMaterialReturnInventoryFormDto
  ): Promise<MaterialReturnInventoryFormResDto[]> {
    return await this.returnInventoryFormService.getList(reqUser, query);
  }

  @ApiOperation({ summary: '删除退库单' })
  @ApiResponse({
    status: 200,
    description: '删除退库单成功'
  })
  @Delete('/form/:id')
  async deleteForm(
    @ReqUser() reqUser: IReqUser,
    @Param('id') id: string
  ): Promise<boolean> {
    return await this.returnInventoryFormService.delete(id, reqUser);
  }

  @ApiOperation({ summary: '提交状态变更' })
  @ApiResponse({
    status: 200,
    description: '提交状态变更成功'
  })
  @Patch('/submit/:id')
  async updateSubmitStatus(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() body: MaterialReturnInventoryFormUpdateSubmitStatusDto
  ) {
    return await this.returnInventoryFormService.updateSubmitStatus(
      id,
      reqUser,
      body
    );
  }

  @ApiOperation({ summary: '审核状态变更' })
  @ApiResponse({
    status: 200,
    description: '审核状态变更成功'
  })
  @Patch('/audit/:id')
  async updateAuditStatus(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() body: MaterialReturnInventoryFormUpdateAuditStatusDto
  ) {
    const { auditStatus } = body;
    return await this.returnInventoryFormService.updateAuditStatus(
      id,
      reqUser,
      auditStatus
    );
  }

  @ApiOperation({ summary: '查询可选的材料分类' })
  @ApiResponse({
    status: 200,
    description: '获取查询可选的材料分类成功',
    type: MaterialReturnInventoryFormChooseCategoryTreeResDto,
    isArray: true
  })
  @Get('/choose/materialCategory')
  async getChooseMaterialCategory(
    @ReqUser() reqUser: IReqUser,
    @Query() query: MaterialReturnInventoryFormChooseCategoryQueryDto
  ): Promise<MaterialReturnInventoryFormChooseCategoryTreeResDto[]> {
    return await this.returnInventoryFormDetailService.getChooseMaterialCategory(
      reqUser,
      query
    );
  }

  @ApiOperation({ summary: '查询可选的材料明细' })
  @ApiResponse({
    status: 200,
    description: '获取查询可选的材料明细成功',
    type: MaterialReturnInventoryFormChooseDetailsResDto,
    isArray: true
  })
  @Get('/choose/materialDetails')
  async getChooseMaterialDetails(
    @Query() query: MaterialReturnInventoryFormChooseDetailsQueryDto,
    @ReqUser() reqUser: IReqUser
  ): Promise<MaterialReturnInventoryFormChooseDetailsResDto[]> {
    return await this.returnInventoryFormDetailService.getChooseMaterialDetails(
      query,
      reqUser
    );
  }

  @ApiOperation({
    summary: '获取附件列表',
    description: '获取附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取附件列表成功',
    type: MaterialReturnInventoryAttachmentResDto,
    isArray: true
  })
  @Get('/attachment/:materialReversalId')
  async getList(
    @Param('materialReversalId') materialReversalId: string,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.getList(materialReversalId, reqUser);
  }

  @ApiOperation({
    summary: '新增附件列表',
    description: '新增附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '新增附件列表成功'
  })
  @Post('/attachment')
  async add(
    @ReqUser() reqUser: IReqUser,
    @Body() data: MaterialReturnInventoryAttachmentCreateDto
  ) {
    return await this.service.add(reqUser, data);
  }

  @ApiOperation({
    summary: '删除附件列表',
    description: '删除附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取附件列表成功'
  })
  @Delete('/attachment/:id')
  async delete(@ReqUser() reqUser: IReqUser, @Param('id') id: string) {
    return await this.service.delete(id, reqUser);
  }
}
