import { Injectable, NotFoundException } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { MaterialReturnInventoryForm } from '@/prisma/generated';

import { MaterialReturnSalesFormChooseDetailsResDto } from '../../material-return-sales/material-return-sales-form-detail/material-return-sales-form-detail.dto';
import {
  MaterialReturnInventoryFormChooseCategoryQueryDto,
  MaterialReturnInventoryFormChooseCategoryTreeResDto,
  MaterialReturnInventoryFormChooseDetailsQueryDto,
  MaterialReturnInventoryFormChooseDetailsResDto
} from '../material-return-inventory-form.dto';
import { ReturnInventoryFormDetailRepository } from '../repositories/return-inventory-form-detail.repository';

interface Material {
  id: string;
  materialId: string;
  materialName: string;
  materialSpec: string;
  unit: string;
  price: number;
  canReversalQuantity: number;
}
@Injectable()
export class ReturnInventoryFormDetailService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: ReturnInventoryFormDetailRepository
  ) {}

  /**
   * 查询可选的材料分类
   * @param returnSalesFormId
   * @param reqUser
   */
  async getChooseMaterialCategory(
    reqUser: IReqUser,
    query: MaterialReturnInventoryFormChooseCategoryQueryDto
  ): Promise<MaterialReturnInventoryFormChooseCategoryTreeResDto[]> {
    // 根据退货单id查询该退货单
    const returnSalesForm = await this.getOne(
      query.materialReversalId,
      reqUser
    );
    const materialCategoryList: MaterialReturnInventoryFormChooseCategoryTreeResDto[] =
      await this.repository.getChooseMaterialCategory(
        returnSalesForm?.supplierId,
        returnSalesForm.returnInventoryType,
        reqUser
      );
    return materialCategoryList;
  }

  /**
   * 查询可选的材料明细
   * @param returnSalesFormId
   * @param reqUser
   */
  async getChooseMaterialDetails(
    query: MaterialReturnInventoryFormChooseDetailsQueryDto,
    reqUser: IReqUser
  ): Promise<MaterialReturnInventoryFormChooseDetailsResDto[]> {
    // 根据退货单id查询该退货单
    const returnSalesForm = await this.getOne(
      query.materialReversalId,
      reqUser
    );
    const materialDetailsList: MaterialReturnInventoryFormChooseDetailsResDto[] =
      await this.repository.getChooseMaterialDetails(
        returnSalesForm?.supplierId,
        query.materialReversalId,
        returnSalesForm.returnInventoryType,
        reqUser,
        query.categoryId
      );
    return await this.mergeMaterials(materialDetailsList);
  }

  // 数据合并
  private async mergeMaterials(
    materials: MaterialReturnInventoryFormChooseDetailsResDto[]
  ): Promise<Material[]> {
    // 创建一个映射表，键为组合的唯一标识，值为合并后的材料对象
    const mergedMap = new Map<string, Material>();

    materials.forEach((material) => {
      // 创建基于 material_id、material_name、material_spec、unit 和 price 的唯一键
      const key = `${material.materialId}-${material.materialName}-${material.materialSpec}-${material.unit}-${material.price}`;

      if (mergedMap.has(key)) {
        // 如果已存在相同组合的材料，则累加 inventory_quantity
        const existingMaterial = mergedMap.get(key)!;
        existingMaterial.canReversalQuantity += material.canReversalQuantity;
      } else {
        // 如果不存在，则添加新的材料记录
        mergedMap.set(key, { ...material });
      }
    });

    // 将映射表中的值转换为数组返回
    return Array.from(mergedMap.values());
  }

  /**
   * 查询单个退库单
   * @param id
   * @param reqUser
   * @returns
   */
  async getOne(
    id: string,
    reqUser: IReqUser
  ): Promise<MaterialReturnInventoryForm> {
    const returnSalesForm =
      await this.prisma.materialReturnInventoryForm.findUnique({
        where: {
          id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        }
      });
    if (!returnSalesForm) {
      throw new NotFoundException('未找到该退库单');
    }
    return returnSalesForm;
  }
}
