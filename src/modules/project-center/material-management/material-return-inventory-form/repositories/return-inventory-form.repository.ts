import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { AuditStatus, Prisma, SubmitStatus } from '@/prisma/generated';

import {
  MaterialReturnInventoryFormResDto,
  MaterialReturnInventoryFormSupplierResDto,
  QueryMaterialReturnInventoryFormDto
} from '../material-return-inventory-form.dto';

@Injectable()
export class ReturnInventoryFormRepository {
  constructor(private readonly prisma: PrismaService) {}

  // 获取供应商名录
  async getSupplierList(
    reqUser: IReqUser
  ): Promise<MaterialReturnInventoryFormSupplierResDto[]> {
    const suppliers = await this.prisma.$queryRaw<
      MaterialReturnInventoryFormSupplierResDto[]
    >`
      select
        sd.id as supplier_id, sd.full_name as supplier_name
      from material_requisition_form mrf
      join supplier_directory sd
      on sd.id = mrf.supplier_id
        and sd.tenant_id = mrf.tenant_id
        and sd.is_deleted = false
      where mrf.is_deleted = false
        and mrf.tenant_id = ${reqUser.tenantId}
        and mrf.org_id = ${reqUser.orgId}
        and mrf.submit_status::TEXT = ${SubmitStatus.SUBMITTED}
        and mrf.audit_status::TEXT = ${AuditStatus.APPROVED}
    `;

    return suppliers;
  }

  async getOne(id: string, reqUser: IReqUser) {
    const res = await this.prisma.$queryRaw<any[]>`
      select mr.*, o.seal_name as project_name, u.nickname as creator from material_return_inventory_form mr
      left join platform_meta.org o
        on o.tenant_id = mr.tenant_id
        and o.id = mr.org_id
      left join platform_meta."user" u
        on u.id = mr.create_By
      where mr.id = ${id}
        and mr.tenant_id = ${reqUser.tenantId}
        and mr.org_id = ${reqUser.orgId}
        and mr.is_deleted = false
    `;
    return res[0];
  }

  async getList(
    query: QueryMaterialReturnInventoryFormDto,
    reqUser: IReqUser
  ): Promise<MaterialReturnInventoryFormResDto[]> {
    const { onlyViewSelf = false } = query;
    const res: MaterialReturnInventoryFormResDto[] = await this.prisma
      .$queryRaw`
       with temp_material_categories as (
        select
          mrd.material_reversal_id,
          STRING_AGG(distinct
            case
              when position('|' in mdc.full_name) > 0
              then split_part(mdc.full_name, '|', 2)
              else split_part(mdc.full_name, '|', 1)
            end,
            ','
          ) filter (where mdc.full_name is not null) material_categories
        from material_return_inventory_form_detail mrd
        join material_dictionary_detail mdd
          on mdd.id = mrd.material_id
          and mdd.is_deleted = false
          and mdd.tenant_id = mrd.tenant_id
        join material_dictionary_category mdc
          on mdc.id = mdd.material_dictionary_category_id
          and mdc.is_deleted = false
          and mdc.tenant_id = mrd.tenant_id
          and mdc.org_id = mdd.org_id
        where mrd.is_deleted = false
          and mrd.tenant_id = ${reqUser.tenantId}
          and mrd.org_id = ${reqUser.orgId}
        group by mrd.material_reversal_id
      )
      select
        mr.settle_status
        ,mr.id
        ,mr.code
        ,o.seal_name as project_name
        ,mr.supplier_name
        ,mr.supplier_id
        ,tmc.material_categories
				,mr.amount
        ,u.nickname as creator
        ,mr.year
        ,mr.month
        ,mr.day
        ,mr.submit_status
        ,mr.audit_status
      from material_return_inventory_form mr
      left join temp_material_categories tmc
        on tmc.material_reversal_id = mr.id
      left join platform_meta.org o
        on o.tenant_id = mr.tenant_id
        and o.id = mr.org_id
      left join platform_meta."user" u
        on u.id = mr.create_By
      where mr.tenant_id = ${reqUser.tenantId}
        and mr.org_id = ${reqUser.orgId}
        and mr.is_deleted = false
        ${onlyViewSelf ? Prisma.sql`and mr.create_By = ${reqUser.id}` : Prisma.empty}
        ${query.year ? Prisma.sql`and mr.year = ${query.year}` : Prisma.empty}
        ${query.month ? Prisma.sql`and mr.month = ${query.month}` : Prisma.empty}
        ${query.day ? Prisma.sql`and mr.day = ${query.day}` : Prisma.empty}
      order by mr.code desc, mr.id desc
    `;
    return res;
  }
}
