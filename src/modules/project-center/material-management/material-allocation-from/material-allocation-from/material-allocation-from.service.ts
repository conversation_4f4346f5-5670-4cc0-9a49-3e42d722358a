import { BadRequestException, Injectable } from '@nestjs/common';
import { Decimal } from '@prisma/client/runtime/library';

import { TimeListResponseDto } from '@/common/dtos/common.dto';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PlatformService } from '@/modules/platform/platform.service';
import {
  AuditStatus,
  MaterialAllocationFrom,
  SubmitStatus
} from '@/prisma/generated';

import { MaterialAllocationFromDetailService } from '../material-allocation-from-detail/material-allocation-from-detail.service';
import {
  MaterialAllocationFormResDto,
  MaterialAllocationFormUpdateDto,
  MaterialAllocationFormUpdateSubmitStatusDto,
  QueryMaterialAllocationFormDto
} from './material-allocation-from.dto';
import { MaterialAllocationFormRepository } from './material-allocation-from.repositories';

@Injectable()
export class MaterialAllocationFromService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: MaterialAllocationFormRepository,
    private readonly materialAllocationFromDetailService: MaterialAllocationFromDetailService
  ) {}

  /**
   * 新增调拨单
   * @param req
   * @param reqUser
   */
  async add(reqUser: IReqUser): Promise<MaterialAllocationFrom> {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    const day = currentDate.getDate();
    // 获取当前调拨单的最新的调拨单编号
    const code = await this.getMaxCode(reqUser, year, month);
    const data = await this.prisma.materialAllocationFrom.create({
      data: {
        year: year,
        month: month,
        day: day,
        code: code,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }
    });
    return await this.repository.getOne(data.id, reqUser, this.prisma);
  }

  /**
   * 编辑调拨单
   * @param id
   * @param reqUser
   * @param data
   * @returns
   */
  async update(
    id: string,
    reqUser: IReqUser,
    data: MaterialAllocationFormUpdateDto
  ): Promise<boolean> {
    // 查询单据收料时间
    const receiving = await this.getOne(id, reqUser);
    if (data.year && data.month && data.month !== receiving.month) {
      data.code = await this.getMaxCode(reqUser, data.year, data.month);
    }
    await this.prisma.materialAllocationFrom.update({
      where: {
        id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      data: {
        ...data,
        updateBy: reqUser.id
      }
    });
    return true;
  }

  async getDateTree(reqUser: IReqUser): Promise<TimeListResponseDto[]> {
    // 实现获取时间列表的逻辑
    const dates = await this.prisma.materialAllocationFrom.findMany({
      distinct: ['year', 'month', 'day'],
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      },
      orderBy: [{ year: 'desc' }, { month: 'desc' }, { day: 'desc' }]
    });

    const resultMap: Record<string, TimeListResponseDto> = {};
    for (const time of dates) {
      // 添加父级，年_月
      if (!resultMap[`${time.year}_${time.month}`]) {
        resultMap[`${time.year}_${time.month}`] = {
          id: `${time.year}_${time.month}`,
          count: await this.getDateCount(reqUser, time.year, time.month),
          parentId: null,
          year: time.year,
          month: time.month
        };
      }

      // 添加子级 年_月_日
      resultMap[`${time.year}_${time.month}_${time.day}`] = {
        id: `${time.year}_${time.month}_${time.day}`,
        count: await this.getDateCount(
          reqUser,
          time.year,
          time.month,
          time.day
        ),
        parentId: `${time.year}_${time.month}`,
        year: time.year,
        month: time.month,
        day: time.day
      };
    }

    return Object.values(resultMap);
  }

  async getList(
    reqUser: IReqUser,
    query: QueryMaterialAllocationFormDto
  ): Promise<MaterialAllocationFormResDto[]> {
    const res: MaterialAllocationFormResDto[] = await this.repository.getList(
      reqUser,
      query,
      this.prisma
    );
    return res;
  }

  /**
   * 删除调拨单
   * @param id
   * @param reqUser
   */
  async delete(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;
    // 删除前校验
    await this.beforeDelete(id, reqUser);
    // 删除退货单必须返还库存
    // 查询退货单下的父级明细
    const parentDetails =
      await this.materialAllocationFromDetailService.getParentList(id, reqUser);
    await this.prisma.$transaction(async (tx) => {
      await tx.materialAllocationFrom.update({
        where: {
          id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      for (const element of parentDetails) {
        await this.materialAllocationFromDetailService.delete(
          element.id,
          reqUser
        );
      }
      await tx.materialAllocationFromAttachment.updateMany({
        where: {
          materialAllocationFromId: id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
    });
    return true;
  }

  /**
   * 修改提交状态
   * @param id
   * @param reqUser
   * @param data
   */
  async updateSubmitStatus(
    id: string,
    reqUser: IReqUser,
    body: MaterialAllocationFormUpdateSubmitStatusDto
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    const { submitStatus } = body;
    const data: {
      submitStatus: SubmitStatus;
      updateBy: string;
    } = {
      submitStatus,
      updateBy: userId
    };
    // 提交状态变更校验逻辑
    await this.beforeUpdateSubmitStatus(id, reqUser, submitStatus);
    await this.prisma.materialAllocationFrom.update({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      },
      data: data
    });
    return true;
  }

  /**
   * 修改审批状态
   * @param id
   * @param reqUser
   * @param data
   */
  async updateAuditStatus(
    id: string,
    reqUser: IReqUser,
    auditStatus: AuditStatus
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    // 审批状态变更校验逻辑
    await this.beforeUpdateAuditStatus(id, reqUser, auditStatus);
    await this.prisma.materialAllocationFrom.update({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      },
      data: {
        auditStatus,
        updateBy: userId
      }
    });
    return true;
  }

  async beforeUpdateAuditStatus(
    id: string,
    reqUser: IReqUser,
    auditStatus: AuditStatus
  ) {
    // 查询调拨单
    const data = await this.getOne(id, reqUser);
    if (
      auditStatus === AuditStatus.PENDING &&
      data.auditStatus === AuditStatus.APPROVED
    ) {
      // 校验是否被下级引用，后面补充
      // 被下级引用的调拨单，不能撤销审批
      // throw new BadRequestException('被下级引用的调拨单，不能撤销审批');
    }
  }

  /**
   * 提交状态变更校验逻辑
   * @param id
   * @param reqUser
   * @param submitStatus
   */
  async beforeUpdateSubmitStatus(
    id: string,
    reqUser: IReqUser,
    submitStatus: SubmitStatus
  ) {
    if (submitStatus === SubmitStatus.SUBMITTED) {
      // 提交状态校验
      await this.beforeSubmit(id, reqUser);
    } else {
      // 取消提交状态校验
      await this.beforeUnSubmit(id, reqUser);
    }
  }

  async beforeSubmit(id: string, reqUser: IReqUser) {
    // 查询调拨单是否有明细
    const detail = await this.materialAllocationFromDetailService.getList(
      id,
      reqUser
    );
    if (!detail.length) {
      throw new BadRequestException('调拨单没有明细，不能提交');
    }
    // 校验审批单的必填项
    if (detail.find((item) => item.allocationQuantity === null)) {
      throw new BadRequestException('存在明细的调拨数量为空,请检查');
    }
  }

  async beforeUnSubmit(id: string, reqUser: IReqUser) {
    // 查询调拨单
    const materialReceiving = await this.getOne(id, reqUser);
    if (materialReceiving.auditStatus === AuditStatus.APPROVED) {
      // 已审批不可取消提交
      throw new BadRequestException('已审批的调拨单，不能取消提交');
    }
  }

  /**
   * 删除前校验
   * @param id
   * @param reqUser
   */
  async beforeDelete(id: string, reqUser: IReqUser) {
    // 查询调拨单
    const returnSalesForm = await this.getOne(id, reqUser);
    if (returnSalesForm.submitStatus !== SubmitStatus.PENDING) {
      throw new BadRequestException('已提交的调拨单，不能删除');
    }
  }

  async getOne(id: string, reqUser: IReqUser) {
    const data = await this.prisma.materialAllocationFrom.findUnique({
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        id
      }
    });
    if (!data) {
      throw new BadRequestException('调拨单不存在');
    }
    return data;
  }

  // 查询时间下的数据数量
  async getDateCount(
    reqUser: IReqUser,
    year: number,
    month: number,
    day?: number
  ): Promise<number> {
    return await this.prisma.materialAllocationFrom.count({
      where: {
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false,
        year: year,
        month: month,
        day: day ? day : undefined
      }
    });
  }

  // 获取当前调拨单的最新的调拨单编号
  async getMaxCode(
    reqUser: IReqUser,
    year: number,
    month: number
  ): Promise<string> {
    const { tenantId, orgId } = reqUser;
    const code = ['调', `${year}${String(month).padStart(2, '0')}`, '001'];
    const maxCode = await this.prisma.materialAllocationFrom.findFirst({
      where: {
        orgId,
        tenantId,
        isDeleted: false,
        year,
        month
      },
      orderBy: {
        code: 'desc'
      }
    });
    if (maxCode?.code.split('-')[1] === code[1]) {
      const lastCode = maxCode.code;
      const lastCodeNumber = parseInt(lastCode.split('-')[2], 10);
      code[2] = String(lastCodeNumber + 1).padStart(3, '0');
    }
    return code.join('-');
  }
}
