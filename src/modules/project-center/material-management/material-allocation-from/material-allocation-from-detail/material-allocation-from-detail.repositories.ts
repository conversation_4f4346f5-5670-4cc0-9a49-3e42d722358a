import { Injectable } from '@nestjs/common';
import { Decimal } from '@prisma/client/runtime/library';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { AuditStatus, Prisma, SubmitStatus } from '@/prisma/generated';

import {
  MaterialAllocationFormChooseCategoryTreeResDto,
  MaterialAllocationFormChooseDetailsResDto,
  MaterialAllocationFormDetailCreateListDto
} from './material-allocation-from-detail.dto';

interface QueryInterface {
  materialName: string;
  materialSpec: string;
  unit: string;
  price: number;
}

@Injectable()
export class MaterialAllocationFormDetailRepository {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 查询可选的材料分类
   * @param materialAllocationFromId
   * @param reqUser
   */
  async getChooseMaterialCategory(
    reqUser: IReqUser
  ): Promise<MaterialAllocationFormChooseCategoryTreeResDto[]> {
    return await this.prisma.$queryRaw<
      MaterialAllocationFormChooseCategoryTreeResDto[]
    >`
        WITH RECURSIVE temp_inventory as (
        -- 查询可用的库存（材料）
          SELECT 
            DISTINCT mdd.material_dictionary_category_id as category_id
          from material_receiving_inventory mri
          join material_dictionary_detail mdd
            on mdd.tenant_id = mri.tenant_id
            and mdd.is_deleted = false
            and mdd.id = mri.material_id
          join material_receiving mr
            on mr.tenant_id = mri.tenant_id
            and mri.receiving_id = mr.id
            and mr.org_id = mri.org_id
            and mr.is_deleted = false
            and mr.submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
            and mr.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
          where mri.is_deleted = false
            and mri.tenant_id = ${reqUser.tenantId}
            and mri.org_id = ${reqUser.orgId}
            and mri.inventory_quantity > 0
        ), 
        -- 根据材料id去重查询分类
        temp_category  as (
          select 
            id
            ,parent_id
            ,code
            ,name
            ,type
            ,remark
            ,level
            ,sort
            ,tenant_id
          from material_dictionary_category
          where 
            tenant_id = ${reqUser.tenantId}
            and is_deleted = false
            and id in(select category_id from temp_inventory)
          
          union all 
          
          select 
            mdc.id
            ,mdc.parent_id
            ,mdc.code
            ,mdc.name
            ,mdc.type
            ,mdc.remark
            ,mdc.level
            ,mdc.sort
            ,mdc.tenant_id
          from material_dictionary_category mdc
          join temp_category t_c
            on t_c.tenant_id = mdc.tenant_id
            and mdc.id = t_c.parent_id
          where 
            mdc.is_deleted = false
        )
        select 
            id
            ,parent_id
            ,code
            ,name
            ,type
            ,remark
          from temp_category
        order by level, sort
    `;
  }

  /**
   * 查询可选的材料明细
   * @param materialAllocationFromId
   * @param reqUser
   */
  async getChooseMaterialDetails(
    materialAllocationFromId: string,
    reqUser: IReqUser,
    categoryId?: string | null
  ) {
    return await this.prisma.$queryRaw<
      MaterialAllocationFormChooseDetailsResDto[]
    >`
        -- 查询可用的库存（材料）
        SELECT 
          mri.receiving_id
          ,mri.material_id
          ,mri.material_name
          ,mri.material_spec
          ,mri.unit
          ,mri.price
          ,mri.inventory_quantity
          ,mdd.material_dictionary_category_id as category_id
          ,EXISTS(
						select id from material_allocation_from_detail 
						where is_deleted = false
            and material_allocation_from_id = ${materialAllocationFromId}
						and tenant_id = ${reqUser.tenantId}
						and org_id = ${reqUser.orgId}
						and material_name = mri.material_name
						and material_spec = mri.material_spec
						and unit = mri.unit
						and in_stock_price = mri.price
					) as disabled
        from material_receiving_inventory mri
        join material_dictionary_detail mdd
          on mdd.tenant_id = mri.tenant_id
          and mdd.is_deleted = false
          and mdd.id = mri.material_id
        join material_dictionary_category mdc
          on mdc.tenant_id = mri.tenant_id
          ${categoryId ? Prisma.sql`and mdc.id = ${categoryId}` : Prisma.empty}
          and mdc.id = mdd.material_dictionary_category_id
          and mdc.is_deleted = false
        join material_receiving mr
          on mr.tenant_id = mri.tenant_id
          and mr.org_id = mri.org_id
          and mr.is_deleted = false
          and mr.submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
          and mr.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
          and mr."id" = mri.receiving_id
        where mri.is_deleted = false
          and mri.tenant_id = ${reqUser.tenantId}
          and mri.org_id = ${reqUser.orgId}
          and mri.inventory_quantity > 0
      `;
  }

  // 查询所有涉及到的材料的收料和退库
  async getMaterialReceivingAndReturnList(
    reqUser: IReqUser,
    list: MaterialAllocationFormDetailCreateListDto[]
  ) {
    const { tenantId, orgId } = reqUser;
    const ml = list.map(
      (m) =>
        `${m.materialName}@@${m.materialSpec}@@${m.unit}@@${m.inStockPrice}`
    );
    const materialReceivingAndReturnList = await this.prisma.$queryRaw<any[]>`
      WITH tmp_receiving as (
        SELECT 
          mri.id as detail_id
          ,mri.id as inventory_id
          ,mri.material_id
          ,mri.material_name
          ,mri.material_spec
          ,mri.unit
          ,mri.price as receiving_price
          ,mri.price
          ,mri.inventory_quantity as quantity
          ,mr.code
          ,mr.create_at as inventory_date
          ,mri.create_at
          ,'RECEIVING' as detail_type
        from material_receiving_inventory mri
        join material_dictionary_detail mdd
          on mdd.tenant_id = mri.tenant_id
          and mdd.is_deleted = false
          and mdd.id = mri.material_id
        join material_receiving mr
          on mr.tenant_id = mri.tenant_id
          and mr.org_id = mri.org_id
          and mr.is_deleted = false
          and mr.submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
          and mr.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
          and mr.id = mri.receiving_id
        where mri.is_deleted = false
          and mri.tenant_id = ${tenantId}
          and mri.org_id = ${orgId}
          and mri.inventory_quantity > 0
          and concat_ws('@@', mri.material_name, mri.material_spec, mri.unit, mri.price) in (
            ${Prisma.join(ml)}
          )
      ),
      tmp_reversal as (
        select
          mrifd.id as detail_id
          ,mrifd.material_receiving_inventory_id as inventory_id
					,mri.material_id
					,mri.material_name
					,mri.material_spec
          ,mri.unit
          ,mri.price as receiving_price
          ,mrifd.reversal_unit as price
          ,mrifd.reversal_inventory_quantity as quantity
          ,mrif.code
          ,mrif.create_at as inventory_date
          ,mri.create_at
          ,'INVENTORY' as detail_type
        from material_return_inventory_form_detail mrifd
        join material_return_inventory_form mrif
            on mrif.id = mrifd.material_reversal_id
            and mrif.submit_status = 'SUBMITTED'::"SubmitStatus"
            and mrif.audit_status = 'APPROVED'::"AuditStatus"
            and mrif.is_deleted = false
            and mrif.org_id = mrifd.org_id
            and mrif.tenant_id = mrifd.tenant_id
        join material_receiving_inventory mri
            on mri.id = mrifd.material_receiving_inventory_id
            and mri.org_id = mrifd.org_id
            and mri.tenant_id = mrifd.tenant_id
            and mri.is_deleted = false
            and concat_ws('@@', mri.material_name, mri.material_spec, mri.unit, mri.price) in (
              ${Prisma.join(ml)}
            )
        join material_receiving mr
          on mr.tenant_id = mri.tenant_id
          and mr.org_id = mri.org_id
          and mr.is_deleted = false
          and mr.submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
          and mr.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
          and mr.id = mri.receiving_id
        where mrifd.is_deleted = false
          and mrifd.tenant_id = ${tenantId}
          and mrifd.org_id = ${orgId}
          and mrifd.parent_id is not null
          and mrifd.reversal_inventory_quantity > 0
      )
      select * from tmp_receiving 
        union all 
      select * from tmp_reversal
      order by detail_type desc, create_at desc
    `;
    return materialReceivingAndReturnList;
  }

  /**
   * 查询现有库存数量
   * @param query
   * @param reqUser
   * @returns
   */
  async getInventory(
    query: QueryInterface,
    reqUser: IReqUser
  ): Promise<number> {
    const { tenantId, orgId } = reqUser;
    const { materialName, materialSpec, unit, price } = query;
    const res = await this.prisma.$queryRaw<any[]>`
      SELECT 
        COALESCE(SUM(mri.inventory_quantity), 0) as total_quantity
        from material_receiving_inventory mri
        join material_dictionary_detail mdd
          on mdd.tenant_id = mri.tenant_id
          and mdd.is_deleted = false
          and mdd.id = mri.material_id
        join material_receiving mr
          on mr.tenant_id = mri.tenant_id
          and mr.org_id = mri.org_id
          and mr.is_deleted = false
          and mr.submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
          and mr.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
          and mr.id = mri.receiving_id
        where mri.is_deleted = false
          and mri.tenant_id = ${tenantId}
          and mri.org_id = ${orgId}
          and mri.inventory_quantity > 0
          and mri.material_name = ${materialName ? materialName : ''}
          and mri.material_spec = ${materialSpec ? materialSpec : ''}
          and mri.unit = ${unit ? unit : ''}
          and mri.price = ${Decimal(price ? price : 0)}
    `;
    return res.length ? res[0].totalQuantity : 0;
  }
}
